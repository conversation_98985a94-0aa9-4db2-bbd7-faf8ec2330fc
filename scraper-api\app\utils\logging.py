"""
Logging configuration and utilities.
"""

import logging
import sys
from pathlib import Path
from typing import Optional

import structlog


def setup_logging(
    log_level: str = "INFO",
    log_format: str = "json",
    log_file: Optional[str] = None,
    enable_console: bool = True
) -> None:
    """
    Set up structured logging for the application.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_format: Log format (json, text)
        log_file: Optional log file path
        enable_console: Whether to enable console logging
    """
    # Configure standard library logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(message)s",
        handlers=[]
    )
    
    # Configure structlog
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
    ]
    
    # Add appropriate renderer based on format
    if log_format.lower() == "json":
        processors.append(structlog.processors.JSONRenderer())
    else:
        processors.append(structlog.dev.ConsoleRenderer())
    
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    # Set up handlers
    handlers = []
    
    # Console handler
    if enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, log_level.upper()))
        handlers.append(console_handler)
    
    # File handler
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(getattr(logging, log_level.upper()))
        handlers.append(file_handler)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.handlers = handlers
    root_logger.setLevel(getattr(logging, log_level.upper()))


def get_logger(name: str) -> structlog.BoundLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Structured logger instance
    """
    return structlog.get_logger(name)


def log_request(logger: structlog.BoundLogger, method: str, path: str, 
                status_code: int, processing_time: float, **kwargs) -> None:
    """
    Log an HTTP request with structured data.
    
    Args:
        logger: Logger instance
        method: HTTP method
        path: Request path
        status_code: Response status code
        processing_time: Request processing time in seconds
        **kwargs: Additional fields to log
    """
    logger.info(
        "HTTP request processed",
        method=method,
        path=path,
        status_code=status_code,
        processing_time=processing_time,
        **kwargs
    )


def log_scraping_result(logger: structlog.BoundLogger, url: str, success: bool,
                       processing_time: float, **kwargs) -> None:
    """
    Log a scraping result with structured data.
    
    Args:
        logger: Logger instance
        url: URL that was scraped
        success: Whether scraping was successful
        processing_time: Scraping processing time in seconds
        **kwargs: Additional fields to log
    """
    logger.info(
        "URL scraped",
        url=url,
        success=success,
        processing_time=processing_time,
        **kwargs
    )


def log_batch_result(logger: structlog.BoundLogger, total_urls: int, 
                    successful: int, failed: int, processing_time: float,
                    **kwargs) -> None:
    """
    Log a batch processing result with structured data.
    
    Args:
        logger: Logger instance
        total_urls: Total number of URLs processed
        successful: Number of successful extractions
        failed: Number of failed extractions
        processing_time: Total processing time in seconds
        **kwargs: Additional fields to log
    """
    logger.info(
        "Batch processed",
        total_urls=total_urls,
        successful=successful,
        failed=failed,
        success_rate=successful / total_urls if total_urls > 0 else 0,
        processing_time=processing_time,
        **kwargs
    )


def log_error(logger: structlog.BoundLogger, error: Exception, 
              context: str = "", **kwargs) -> None:
    """
    Log an error with structured data.
    
    Args:
        logger: Logger instance
        error: Exception that occurred
        context: Context where the error occurred
        **kwargs: Additional fields to log
    """
    logger.error(
        "Error occurred",
        error_type=type(error).__name__,
        error_message=str(error),
        context=context,
        **kwargs,
        exc_info=True
    )
