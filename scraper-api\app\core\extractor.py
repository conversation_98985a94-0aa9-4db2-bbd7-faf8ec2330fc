"""
Contact extraction engine - extracted and refactored from PerfectContactExtractor.
Removes UI dependencies and focuses on pure extraction logic.
"""

import asyncio
import json
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Union
from urllib.parse import urljoin, urlparse

from crawl4ai import Async<PERSON>ebCrawler, CrawlerRunConfig, JsonCssExtractionStrategy


class ContactExtractor:
    """
    Core contact extraction engine.
    Extracted from PerfectContactExtractor with UI dependencies removed.
    """
    
    def __init__(self, batch_size: int = 50, max_concurrent: int = 6):
        """Initialize the contact extractor."""
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        
        # Website type detection and adaptive page templates
        self.page_templates = {
            'business': [
                ('', 10),              # Main page - highest priority
                ('/contact-us', 9),    # Primary contact page
                ('/contact', 9),       # Alternative contact page
                ('/about', 7),         # About page often has contact info
            ],
            'government': [
                ('', 10),              # Main page
                ('/contact', 9),       # Contact page
                ('/staff', 8),         # Staff directory
                ('/directory', 8),     # Staff/contact directory
                ('/departments', 7),   # Department listings
                ('/services', 7),      # Service information
                ('/about', 6),         # About page
            ],
            'medical': [
                ('', 10),              # Main page
                ('/contact', 9),       # Contact page
                ('/providers', 8),     # Doctor/provider listings
                ('/doctors', 8),       # Doctor directory
                ('/locations', 8),     # Office locations
                ('/offices', 7),       # Office information
                ('/appointments', 7),  # Appointment info
                ('/patients', 6),      # Patient information
                ('/about', 6),         # About page
            ],
            'education': [
                ('', 10),              # Main page
                ('/contact', 9),       # Contact page
                ('/staff', 8),         # Staff directory
                ('/faculty', 8),       # Faculty listings
                ('/administration', 7), # Admin contacts
                ('/directory', 7),     # Contact directory
                ('/about', 6),         # About page
            ]
        }
    
    def _detect_website_type(self, url: str) -> str:
        """Detect the type of website to use appropriate page templates."""
        url_lower = url.lower()
        
        # Government sites
        if '.gov' in url_lower or 'government' in url_lower:
            return 'government'
        
        # Educational institutions
        if '.edu' in url_lower or any(keyword in url_lower for keyword in ['university', 'college', 'school']):
            return 'education'
        
        # Medical/healthcare sites
        medical_keywords = [
            'medical', 'health', 'hospital', 'clinic', 'doctor', 'physician',
            'dental', 'dentist', 'orthodont', 'surgery', 'surgical', 'care',
            'wellness', 'therapy', 'treatment', 'patient', 'healthcare',
            'providers', 'upmc', 'mayo', 'cleveland', 'johns-hopkins'
        ]
        if any(keyword in url_lower for keyword in medical_keywords):
            return 'medical'
        
        # Default to business
        return 'business'
    
    def _get_adaptive_pages(self, url: str) -> List[Tuple[str, int]]:
        """Get adaptive page list based on website type."""
        website_type = self._detect_website_type(url)
        return self.page_templates.get(website_type, self.page_templates['business'])
    
    async def extract_contacts(self, urls: Union[str, List[str]], 
                             max_pages: int = 5,
                             timeout: int = 30,
                             include_social: bool = True,
                             include_phone: bool = True) -> List[Dict]:
        """
        Extract contact information from URLs.
        
        Args:
            urls: URLs to process
            max_pages: Maximum pages to check per URL
            timeout: Timeout per page in seconds
            include_social: Whether to extract social media
            include_phone: Whether to extract phone numbers
            
        Returns:
            List of extraction results
        """
        if isinstance(urls, str):
            urls = [urls]
        
        all_results = []
        total_start = datetime.now()
        
        # Process URLs in batches
        for i in range(0, len(urls), self.batch_size):
            batch = urls[i:i + self.batch_size]
            batch_results = await self._process_batch(
                batch, max_pages, timeout, include_social, include_phone
            )
            all_results.extend(batch_results)
            
            # Small delay between batches
            if i + self.batch_size < len(urls):
                await asyncio.sleep(1)
        
        return all_results
    
    async def _process_batch(self, urls: List[str], max_pages: int, timeout: int,
                           include_social: bool, include_phone: bool) -> List[Dict]:
        """Process a batch of URLs with concurrency control."""
        
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def process_single(url: str) -> Dict:
            async with semaphore:
                return await self._extract_single_url(
                    url, max_pages, timeout, include_social, include_phone
                )
        
        tasks = [process_single(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                final_results.append({
                    "url": urls[i],
                    "success": False,
                    "error": str(result),
                    "timestamp": datetime.now().isoformat()
                })
            else:
                final_results.append(result)
        
        return final_results

    async def _extract_single_url(self, url: str, max_pages: int, timeout: int,
                                include_social: bool, include_phone: bool) -> Dict:
        """Extract contact information from a single URL."""

        try:
            # Get adaptive pages based on website type
            adaptive_pages = self._get_adaptive_pages(url)

            # Limit pages based on max_pages parameter
            pages_to_check = adaptive_pages[:max_pages]

            # Build pages to check in priority order
            pages_to_check_urls = []
            for page_path, priority in pages_to_check:
                if page_path == '':
                    page_url = url
                else:
                    page_url = urljoin(url, page_path)
                pages_to_check_urls.append((page_url, priority))

            best_email = None
            best_phone = None if include_phone else {"phone": "disabled", "confidence": 0.0}
            best_social = None if include_social else {"platform": "disabled", "confidence": 0.0}
            pages_checked = 0
            pages_with_content = 0

            async with AsyncWebCrawler(verbose=False) as crawler:
                for page_url, priority in pages_to_check_urls:
                    try:
                        # Extract from this page with adaptive timeout
                        page_timeout = timeout if page_url == url else min(timeout, 10)
                        result = await asyncio.wait_for(
                            self._extract_from_page(crawler, page_url, include_social, include_phone),
                            timeout=page_timeout
                        )

                        pages_checked += 1

                        if result.get('has_content'):
                            pages_with_content += 1

                        # Update best contacts
                        if result.get('email') and not best_email:
                            best_email = result['email']

                        if include_phone and result.get('phone') and not best_phone:
                            best_phone = result['phone']

                        if include_social and result.get('social') and not best_social:
                            best_social = result['social']

                        # Smart early stopping logic
                        if best_email and (not include_phone or best_phone) and (not include_social or best_social):
                            break
                        elif pages_checked >= 2 and best_email:
                            # If we have email after checking main + contact page, that's often enough
                            remaining_contact_pages = [p for p in pages_to_check_urls[pages_checked:] if 'contact' in p[0]]
                            if not remaining_contact_pages:
                                break

                    except asyncio.TimeoutError:
                        continue
                    except Exception:
                        continue

            return {
                "url": url,
                "success": True,
                "timestamp": datetime.now().isoformat(),
                "data": {
                    "email": best_email,
                    "phone": best_phone,
                    "social_media": best_social
                },
                "metadata": {
                    "pages_available": len(pages_to_check),
                    "pages_checked": pages_checked,
                    "pages_with_content": pages_with_content,
                    "efficiency": f"{pages_checked}/{len(pages_to_check)}",
                    "website_type": self._detect_website_type(url)
                }
            }

        except Exception as e:
            return {
                "url": url,
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def _extract_from_page(self, crawler: AsyncWebCrawler, url: str,
                               include_social: bool, include_phone: bool) -> Dict:
        """Extract contact information from a single page."""

        # Enhanced CSS extraction strategy
        css_schema = {
            "name": "Contact Extraction",
            "baseSelector": "body",
            "fields": [
                {
                    "name": "emails",
                    "selector": "a[href^='mailto:'], [data-email], [href*='@'], .email, .contact-email",
                    "type": "list",
                    "fields": [{"name": "email", "type": "attribute", "attribute": "href"}]
                }
            ]
        }

        # Add phone extraction if enabled
        if include_phone:
            css_schema["fields"].append({
                "name": "phones",
                "selector": "a[href^='tel:'], [data-phone], .phone, .contact-phone, .telephone",
                "type": "list",
                "fields": [{"name": "phone", "type": "attribute", "attribute": "href"}]
            })

        # Add social media extraction if enabled
        if include_social:
            css_schema["fields"].append({
                "name": "social",
                "selector": "a[href*='instagram.com'], a[href*='facebook.com'], a[href*='twitter.com'], a[href*='x.com'], a[href*='linkedin.com'], a[href*='youtube.com'], a[href*='tiktok.com'], a[href*='pinterest.com'], a[href*='yelp.com'], .social-links a, .social-media a, [class*='social'] a",
                "type": "list",
                "fields": [{"name": "url", "type": "attribute", "attribute": "href"}]
            })

        strategy = JsonCssExtractionStrategy(css_schema, verbose=False)
        config = CrawlerRunConfig(extraction_strategy=strategy)

        try:
            result = await crawler.arun(url=url, config=config)
        except Exception:
            return {'email': None, 'phone': None, 'social': None, 'has_content': False}

        best_email = None
        best_phone = None
        best_social = None
        has_content = False

        # Check if page was successfully loaded
        if result.success and result.status_code and result.status_code < 400:
            has_content = True
        elif result.status_code == 404:
            return {'email': None, 'phone': None, 'social': None, 'has_content': False}
        elif result.success:
            has_content = True

            # Process CSS results
            if result.extracted_content:
                try:
                    data = json.loads(result.extracted_content)
                    if data and len(data) > 0:
                        css_data = data[0]

                        # Extract emails
                        emails = set()
                        for email_item in css_data.get('emails', []):
                            if isinstance(email_item, dict):
                                email = email_item.get('email', '').replace('mailto:', '').strip().lower()
                                if self._is_valid_email(email):
                                    emails.add(email)

                        if emails:
                            best_email = self._select_best_email(list(emails))

                        # Extract phone numbers if enabled
                        if include_phone:
                            phones = set()
                            for phone_item in css_data.get('phones', []):
                                if isinstance(phone_item, dict):
                                    phone = phone_item.get('phone', '').replace('tel:', '').strip()
                                    if self._is_valid_phone(phone):
                                        phones.add(phone)

                            if phones:
                                best_phone = self._select_best_phone(list(phones))

                        # Extract social links if enabled
                        if include_social:
                            social_links = set()
                            for social_item in css_data.get('social', []):
                                if isinstance(social_item, dict):
                                    social_url = social_item.get('url', '').strip()
                                    if social_url and self._is_valid_social_url(social_url):
                                        social_links.add(social_url)

                            if social_links:
                                best_social = self._select_best_social(list(social_links))

                except json.JSONDecodeError:
                    pass

            # Regex fallback extraction
            if (not best_email or (include_phone and not best_phone) or (include_social and not best_social)) and hasattr(result, 'cleaned_html'):
                content = result.cleaned_html

                # Email regex fallback
                if not best_email:
                    email_patterns = [
                        r'\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b',
                        r'(?:email|contact|reach|write|talk)[\s:]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                        r'mailto:([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                    ]

                    all_emails = set()
                    for pattern in email_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        for match in matches:
                            email = match if isinstance(match, str) else match[0] if isinstance(match, tuple) else str(match)
                            if self._is_valid_email(email.lower()):
                                all_emails.add(email.lower())

                    if all_emails:
                        best_email = self._select_best_email(list(all_emails))

                # Phone regex fallback
                if include_phone and not best_phone:
                    phone_patterns = [
                        r'(?:phone|tel|call)[\s:]*(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})',
                        r'(\d{3}[-.\s]\d{3}[-.\s]\d{4})',
                        r'(\(\d{3}\)\s?\d{3}[-.\s]\d{4})',
                        r'(\+?1[-.\s]?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})',
                    ]

                    all_phones = set()
                    for pattern in phone_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        for match in matches:
                            phone = match if isinstance(match, str) else match[0] if isinstance(match, tuple) else str(match)
                            if self._is_valid_phone(phone):
                                all_phones.add(phone)

                    if all_phones:
                        best_phone = self._select_best_phone(list(all_phones))

                # Social media regex fallback
                if include_social and not best_social:
                    social_pattern = r'https?://(?:www\.)?(?:instagram|facebook|twitter|x|linkedin|youtube|tiktok|pinterest|yelp)\.com/[^\s<>"\']+'
                    social_matches = re.findall(social_pattern, content, re.IGNORECASE)
                    valid_socials = [s for s in social_matches[:5] if self._is_valid_social_url(s)]
                    if valid_socials:
                        best_social = self._select_best_social(valid_socials)

        return {
            'email': best_email,
            'phone': best_phone,
            'social': best_social,
            'has_content': has_content
        }

    def _is_valid_email(self, email: str) -> bool:
        """Validate email address."""
        if not email or len(email) < 5 or len(email) > 50:
            return False

        # Enhanced blacklist
        blacklist = ['.svg', '.png', '.jpg', '.jpeg', '.gif', '.css', '.js', '.pdf', '.doc', '.zip']
        if any(ext in email for ext in blacklist):
            return False

        # Must have exactly one @
        if '@' not in email or email.count('@') != 1:
            return False

        # Format validation
        return bool(re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email))

    def _is_valid_phone(self, phone: str) -> bool:
        """Validate phone number."""
        if not phone:
            return False

        # Remove all non-digit characters for validation
        digits_only = re.sub(r'\D', '', phone)

        # Valid phone numbers should have 7-15 digits (international range)
        if not (7 <= len(digits_only) <= 15):
            return False

        # Avoid obviously invalid patterns
        if digits_only in ['0000000000', '1111111111', '1234567890']:
            return False

        return True

    def _is_valid_social_url(self, url: str) -> bool:
        """Validate social media URL."""
        if not url or len(url) < 10:
            return False

        # Supported platforms
        platforms = [
            'instagram.com', 'facebook.com', 'twitter.com', 'x.com',
            'linkedin.com', 'youtube.com', 'tiktok.com', 'pinterest.com',
            'yelp.com', 'google.com/maps'
        ]
        if not any(platform in url.lower() for platform in platforms):
            return False

        # Avoid unwanted patterns
        avoid_patterns = [
            '/p/', '/posts/', '/photo/', '/status/', '/tweet/', '/reel/',
            '/watch?v=', '/pin/', '/review/', '/photos/', '/events/',
            '/ads/', '/business/help/', '/privacy/', '/terms/', '/support/',
            'sharer.php', 'intent/tweet', 'share?'
        ]
        if any(pattern in url.lower() for pattern in avoid_patterns):
            return False

        return True

    def _select_best_email(self, emails: List[str]) -> Optional[Dict]:
        """Select the best email from candidates."""
        if not emails:
            return None

        # Priority prefixes
        priority_prefixes = ['contact', 'info', 'hello', 'admin', 'support', 'talk', 'team']

        for prefix in priority_prefixes:
            for email in emails:
                if email.startswith(f"{prefix}@"):
                    return {"email": email, "confidence": 0.95}

        # Return shortest, most professional email
        best = min(emails, key=lambda x: (len(x), x.count('.'), 'noreply' in x))
        confidence = 0.85 if 'noreply' not in best else 0.70
        return {"email": best, "confidence": confidence}

    def _select_best_phone(self, phones: List[str]) -> Optional[Dict]:
        """Select the best phone number from candidates."""
        if not phones:
            return None

        # Prefer formatted phone numbers
        formatted_phones = [p for p in phones if any(char in p for char in ['-', '(', ')', '.', ' '])]
        if formatted_phones:
            best_phone = formatted_phones[0]
        else:
            best_phone = phones[0]

        return {"phone": best_phone, "confidence": 0.90}

    def _select_best_social(self, social_links: List[str]) -> Optional[Dict]:
        """Select the best social media link from candidates."""
        if not social_links:
            return None

        # Platform priority (business-focused order)
        priority = [
            'instagram.com', 'facebook.com', 'linkedin.com',
            'twitter.com', 'x.com', 'youtube.com', 'yelp.com',
            'tiktok.com', 'pinterest.com', 'google.com/maps'
        ]

        for platform in priority:
            for link in social_links:
                if platform in link.lower():
                    platform_name = self._normalize_platform_name(platform)
                    handle = self._extract_handle(link, platform_name)
                    confidence = self._get_platform_confidence(platform_name)
                    return {
                        "platform": platform_name,
                        "url": link,
                        "handle": handle,
                        "confidence": confidence
                    }

        return None

    def _normalize_platform_name(self, platform: str) -> str:
        """Normalize platform names for consistency."""
        platform_map = {
            'instagram.com': 'instagram',
            'facebook.com': 'facebook',
            'linkedin.com': 'linkedin',
            'twitter.com': 'twitter',
            'x.com': 'twitter',  # Normalize X to Twitter
            'youtube.com': 'youtube',
            'tiktok.com': 'tiktok',
            'pinterest.com': 'pinterest',
            'yelp.com': 'yelp',
            'google.com/maps': 'google_business'
        }
        return platform_map.get(platform, platform.split('.')[0])

    def _get_platform_confidence(self, platform: str) -> float:
        """Get confidence score based on platform business relevance."""
        confidence_map = {
            'instagram': 0.95,
            'facebook': 0.95,
            'linkedin': 0.90,
            'twitter': 0.85,
            'youtube': 0.80,
            'yelp': 0.90,
            'tiktok': 0.75,
            'pinterest': 0.70,
            'google_business': 0.95
        }
        return confidence_map.get(platform, 0.80)

    def _extract_handle(self, url: str, platform: str = '') -> Optional[str]:
        """Extract social media handle with platform-specific logic."""
        try:
            parsed = urlparse(url)
            path_parts = [p for p in parsed.path.strip('/').split('/') if p]

            if not path_parts:
                return None

            # Platform-specific handle extraction
            if platform == 'youtube':
                if path_parts[0] in ['channel', 'c', 'user'] and len(path_parts) > 1:
                    return f"@{path_parts[1]}"
                elif path_parts[0] not in ['watch', 'playlist', 'shorts']:
                    return f"@{path_parts[0]}"
            elif platform == 'linkedin':
                if path_parts[0] in ['company', 'in'] and len(path_parts) > 1:
                    return f"@{path_parts[1]}"
            elif platform == 'yelp':
                if path_parts[0] == 'biz' and len(path_parts) > 1:
                    return f"@{path_parts[1]}"
            elif platform == 'google_business':
                return "@google_business"
            else:
                # Standard social media platforms
                if path_parts[0] not in ['share', 'sharer', 'intent', 'pages']:
                    return f"@{path_parts[0]}"

            return None
        except:
            return None
