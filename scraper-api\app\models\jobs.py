"""
Job-related models for async batch processing.
"""

from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

from .responses import SingleUrlResponse, BatchSummary


class JobStatusEnum(str, Enum):
    """Job status enumeration."""
    QUEUED = "queued"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class JobProgress(BaseModel):
    """Job progress information."""
    
    total_urls: int = Field(description="Total number of URLs in the job")
    processed_urls: int = Field(description="Number of URLs processed so far")
    successful: int = Field(description="Number of successful extractions")
    failed: int = Field(description="Number of failed extractions")
    percentage: float = Field(
        ge=0.0, 
        le=100.0, 
        description="Completion percentage"
    )


class JobStatus(BaseModel):
    """Job status information."""
    
    job_id: str = Field(description="Unique job identifier")
    status: JobStatusEnum = Field(description="Current job status")
    
    progress: Optional[JobProgress] = Field(
        default=None,
        description="Job progress information"
    )
    
    created_at: datetime = Field(description="When the job was created")
    started_at: Optional[datetime] = Field(
        default=None,
        description="When the job started processing"
    )
    completed_at: Optional[datetime] = Field(
        default=None,
        description="When the job completed"
    )
    
    estimated_completion: Optional[datetime] = Field(
        default=None,
        description="Estimated completion time"
    )
    
    processing_time: Optional[float] = Field(
        default=None,
        description="Total processing time in seconds"
    )
    
    error_message: Optional[str] = Field(
        default=None,
        description="Error message if job failed"
    )


class JobResult(BaseModel):
    """Job results with individual URL results and summary."""
    
    job_id: str = Field(description="Unique job identifier")
    status: JobStatusEnum = Field(description="Job status")
    
    total_results: int = Field(description="Total number of results")
    page: int = Field(default=1, description="Current page number")
    limit: int = Field(default=100, description="Results per page")
    
    results: List[SingleUrlResponse] = Field(
        description="Individual results for each URL"
    )
    
    summary: Optional[BatchSummary] = Field(
        default=None,
        description="Summary of all results"
    )
    
    created_at: datetime = Field(description="When the job was created")
    completed_at: Optional[datetime] = Field(
        default=None,
        description="When the job completed"
    )
    
    processing_time: Optional[float] = Field(
        default=None,
        description="Total processing time in seconds"
    )
