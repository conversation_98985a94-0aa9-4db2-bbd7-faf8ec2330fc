"""
Basic test script to verify the API works.
"""

import asyncio
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

async def test_core_functionality():
    """Test the core scraper functionality without FastAPI."""
    
    print("🧪 TESTING CORE API FUNCTIONALITY")
    print("=" * 50)
    
    try:
        # Test imports
        print("📦 Testing imports...")
        from app.core.scraper import WebScraperEngine
        from app.core.extractor import ContactExtractor
        from app.utils.validation import validate_url
        from app.config import get_settings
        print("✅ All imports successful")
        
        # Test configuration
        print("\n⚙️ Testing configuration...")
        settings = get_settings()
        print(f"✅ Configuration loaded: {settings.api_title} v{settings.api_version}")
        
        # Test URL validation
        print("\n🔍 Testing URL validation...")
        test_urls = [
            "https://example.com",
            "http://httpbin.org",
            "invalid-url",
            "ftp://example.com"
        ]
        
        for url in test_urls:
            is_valid = validate_url(url)
            status = "✅ Valid" if is_valid else "❌ Invalid"
            print(f"  {url}: {status}")
        
        # Test scraper engine creation
        print("\n🚀 Testing scraper engine...")
        engine = WebScraperEngine(
            batch_size=2,
            max_concurrent=1,
            default_timeout=10,
            default_max_pages=2
        )
        print("✅ Scraper engine created successfully")
        
        # Test stats
        stats = engine.get_stats()
        print(f"✅ Engine stats: {stats}")
        
        # Test single URL scraping with a simple URL
        print("\n🎯 Testing single URL scraping...")
        test_url = "https://httpbin.org"
        
        try:
            result = await engine.scrape_single_url(
                url=test_url,
                max_pages=1,
                timeout=15,
                include_social=True,
                include_phone=True
            )
            
            print(f"✅ Scraping completed for {test_url}")
            print(f"   Success: {result.get('success')}")
            print(f"   Processing time: {result.get('processing_time', 0):.2f}s")
            
            if result.get('success'):
                data = result.get('data', {})
                metadata = result.get('metadata', {})
                print(f"   Email found: {'Yes' if data.get('email') else 'No'}")
                print(f"   Phone found: {'Yes' if data.get('phone') else 'No'}")
                print(f"   Social found: {'Yes' if data.get('social_media') else 'No'}")
                print(f"   Pages checked: {metadata.get('pages_checked', 0)}")
                print(f"   Website type: {metadata.get('website_type', 'unknown')}")
            else:
                error = result.get('error', {})
                print(f"   Error: {error.get('message', 'Unknown error')}")
        
        except Exception as e:
            print(f"❌ Single URL test failed: {e}")
        
        # Test batch scraping with simple URLs
        print("\n📦 Testing batch URL scraping...")
        batch_urls = [
            "https://httpbin.org",
            "https://example.com"
        ]
        
        try:
            batch_result = await engine.scrape_batch_urls(
                urls=batch_urls,
                max_pages=1,
                timeout=10,
                include_social=True,
                include_phone=True
            )
            
            print(f"✅ Batch scraping completed")
            print(f"   Success: {batch_result.get('success')}")
            print(f"   Total processing time: {batch_result.get('total_processing_time', 0):.2f}s")
            
            if batch_result.get('success'):
                summary = batch_result.get('summary', {})
                print(f"   Total URLs: {summary.get('total_urls', 0)}")
                print(f"   Successful: {summary.get('successful', 0)}")
                print(f"   Failed: {summary.get('failed', 0)}")
                print(f"   Emails found: {summary.get('emails_found', 0)}")
            else:
                error = batch_result.get('error', {})
                print(f"   Error: {error.get('message', 'Unknown error')}")
        
        except Exception as e:
            print(f"❌ Batch URL test failed: {e}")
        
        print("\n" + "=" * 50)
        print("✅ CORE FUNCTIONALITY TEST COMPLETED")
        print("The API core is working and ready for deployment!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ CORE FUNCTIONALITY TEST FAILED")
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_api_models():
    """Test the API models."""
    
    print("\n🏗️ TESTING API MODELS")
    print("=" * 30)
    
    try:
        from app.models.requests import SingleUrlRequest, BatchUrlRequest, ScrapingOptions
        from app.models.responses import SingleUrlResponse, ContactData, EmailData
        
        # Test request models
        print("📝 Testing request models...")
        
        # Single URL request
        single_request = SingleUrlRequest(
            url="https://example.com",
            options=ScrapingOptions(
                max_pages=3,
                timeout=20,
                include_social=True,
                include_phone=True
            )
        )
        print(f"✅ Single URL request: {single_request.url}")
        
        # Batch URL request
        batch_request = BatchUrlRequest(
            urls=["https://example.com", "https://httpbin.org"],
            options=ScrapingOptions(max_pages=2)
        )
        print(f"✅ Batch URL request: {len(batch_request.urls)} URLs")
        
        # Test response models
        print("📤 Testing response models...")
        
        email_data = EmailData(email="<EMAIL>", confidence=0.95)
        contact_data = ContactData(email=email_data)
        print(f"✅ Contact data: {contact_data.email.email}")
        
        print("✅ All API models working correctly")
        return True
        
    except Exception as e:
        print(f"❌ API models test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    
    print("🚀 STARTING API TESTS")
    print("Testing the new Web Scraper API...")
    
    # Test core functionality
    core_success = await test_core_functionality()
    
    # Test API models
    models_success = await test_api_models()
    
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    if core_success and models_success:
        print("✅ ALL TESTS PASSED!")
        print("🎉 The Web Scraper API is ready for deployment!")
        print("\nNext steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Start the API: python -m app.main")
        print("3. Visit http://localhost:8000/docs for interactive documentation")
    else:
        print("❌ SOME TESTS FAILED")
        print("Please check the errors above and fix them before deployment.")
    
    return core_success and models_success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
