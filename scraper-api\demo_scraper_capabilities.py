#!/usr/bin/env python3
"""
Web Scraper API Capabilities Demonstration
Tests the Docker-based scraper with various URL types to showcase extraction capabilities.
"""

import requests
import json
import time
from typing import Dict, List, Any
from datetime import datetime
import sys

API_BASE_URL = "http://localhost:8000"

def load_test_urls() -> Dict[str, Any]:
    """Load test URLs from JSON file."""
    try:
        with open('test_urls.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ test_urls.json not found. Make sure it's in the same directory.")
        sys.exit(1)

def check_api_health() -> bool:
    """Check if the API is running and healthy."""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        return response.status_code == 200
    except requests.exceptions.RequestException:
        return False

def test_single_url(url: str, description: str = "") -> Dict[str, Any]:
    """Test scraping a single URL."""
    try:
        payload = {
            "url": url,
            "options": {
                "timeout": 15,
                "max_pages": 2
            }
        }
        
        start_time = time.time()
        response = requests.post(
            f"{API_BASE_URL}/scrape/single",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        response.raise_for_status()
        processing_time = time.time() - start_time
        
        data = response.json()
        return {
            "success": True,
            "url": url,
            "description": description,
            "api_success": data.get('success', False),
            "processing_time": processing_time,
            "data": data.get('data'),
            "error": data.get('error')
        }
    except Exception as e:
        return {
            "success": False,
            "url": url,
            "description": description,
            "error": str(e),
            "processing_time": 0
        }

def test_batch_urls(urls: List[str], batch_name: str = "") -> Dict[str, Any]:
    """Test batch scraping of multiple URLs."""
    try:
        payload = {
            "urls": urls,
            "batch_options": {
                "batch_size": min(10, len(urls)),
                "max_concurrent": 5
            },
            "scraping_options": {
                "timeout": 15,
                "max_pages": 2
            }
        }
        
        start_time = time.time()
        response = requests.post(
            f"{API_BASE_URL}/scrape/batch",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=120
        )
        response.raise_for_status()
        processing_time = time.time() - start_time
        
        data = response.json()
        return {
            "success": True,
            "batch_name": batch_name,
            "total_urls": len(urls),
            "processing_time": processing_time,
            "api_success": data.get('success', False),
            "results": data.get('results', [])
        }
    except Exception as e:
        return {
            "success": False,
            "batch_name": batch_name,
            "error": str(e),
            "processing_time": 0
        }

def analyze_results(result: Dict[str, Any]) -> Dict[str, int]:
    """Analyze extraction results."""
    if not result.get('data'):
        return {"emails": 0, "phones": 0, "social_links": 0}
    
    data = result['data']
    return {
        "emails": len(data.get('emails', [])),
        "phones": len(data.get('phones', [])),
        "social_links": len(data.get('social_links', []))
    }

def print_single_result(result: Dict[str, Any]):
    """Print results for a single URL test."""
    status = "✅" if result['success'] and result.get('api_success') else "❌"
    url = result['url'][:60] + "..." if len(result['url']) > 60 else result['url']
    
    print(f"{status} {url}")
    if result['description']:
        print(f"   📝 {result['description']}")
    
    if result['success'] and result.get('api_success'):
        analysis = analyze_results(result)
        print(f"   📊 Found: {analysis['emails']} emails, {analysis['phones']} phones, {analysis['social_links']} social links")
        print(f"   ⏱️  Processing time: {result['processing_time']:.2f}s")
        
        # Show some actual extracted data if available
        if result.get('data'):
            data = result['data']
            if data.get('emails'):
                print(f"   📧 Emails: {', '.join(data['emails'][:3])}{'...' if len(data['emails']) > 3 else ''}")
            if data.get('phones'):
                print(f"   📞 Phones: {', '.join(data['phones'][:2])}{'...' if len(data['phones']) > 2 else ''}")
            if data.get('social_links'):
                social_types = list(set([link.get('platform', 'unknown') for link in data['social_links']]))
                print(f"   🔗 Social: {', '.join(social_types[:3])}{'...' if len(social_types) > 3 else ''}")
    else:
        error_msg = result.get('error', 'Unknown error')
        if isinstance(error_msg, dict):
            error_msg = error_msg.get('message', str(error_msg))
        print(f"   ❌ Error: {error_msg}")
    print()

def print_batch_results(result: Dict[str, Any]):
    """Print results for a batch test."""
    status = "✅" if result['success'] and result.get('api_success') else "❌"
    print(f"{status} Batch Test: {result.get('batch_name', 'Unnamed')}")
    print(f"   📊 URLs: {result.get('total_urls', 0)}")
    print(f"   ⏱️  Total time: {result.get('processing_time', 0):.2f}s")
    
    if result['success'] and result.get('results'):
        successful = sum(1 for r in result['results'] if r.get('success'))
        total_contacts = 0
        total_emails = 0
        total_phones = 0
        total_social = 0
        
        for r in result['results']:
            if r.get('success') and r.get('data'):
                data = r['data']
                total_emails += len(data.get('emails', []))
                total_phones += len(data.get('phones', []))
                total_social += len(data.get('social_links', []))
        
        total_contacts = total_emails + total_phones + total_social
        
        print(f"   ✅ Successful: {successful}/{result['total_urls']}")
        print(f"   📧 Total emails found: {total_emails}")
        print(f"   📞 Total phones found: {total_phones}")
        print(f"   🔗 Total social links found: {total_social}")
        print(f"   📈 Avg time per URL: {result['processing_time']/result['total_urls']:.2f}s")
    else:
        error_msg = result.get('error', 'Unknown error')
        print(f"   ❌ Error: {error_msg}")
    print()

def demo_single_url_tests(test_data: Dict[str, Any]):
    """Demonstrate single URL testing across different categories."""
    print("🔍 SINGLE URL TESTING DEMONSTRATION")
    print("=" * 60)
    
    categories = test_data['test_categories']
    
    for category_name, category_data in categories.items():
        print(f"\n📂 Category: {category_name.replace('_', ' ').title()}")
        print(f"   {category_data['description']}")
        print("-" * 50)
        
        for url_data in category_data['urls'][:2]:  # Test first 2 URLs from each category
            result = test_single_url(url_data['url'], url_data.get('notes', ''))
            print_single_result(result)
            time.sleep(1)  # Brief pause between requests

def demo_batch_testing(test_data: Dict[str, Any]):
    """Demonstrate batch processing capabilities."""
    print("\n🚀 BATCH PROCESSING DEMONSTRATION")
    print("=" * 60)
    
    batch_sets = test_data['batch_test_sets']
    
    for batch_name, batch_data in batch_sets.items():
        print(f"\n📦 {batch_data['description']}")
        print("-" * 50)
        
        result = test_batch_urls(batch_data['urls'], batch_name)
        print_batch_results(result)
        
        if batch_name == 'quick_demo':  # Only run quick demo by default
            break

def main():
    """Run the complete demonstration."""
    print("🚀 WEB SCRAPER API CAPABILITIES DEMONSTRATION")
    print("=" * 70)
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check API health
    if not check_api_health():
        print("❌ API is not accessible. Make sure the Docker container is running:")
        print("   docker run -d -p 8000:8000 --name scraper-api-test scraper-api")
        return
    
    print("✅ API is healthy and ready for testing!")
    print()
    
    # Load test data
    test_data = load_test_urls()
    
    # Run demonstrations
    demo_single_url_tests(test_data)
    demo_batch_testing(test_data)
    
    print("🎯 DEMONSTRATION SUMMARY")
    print("=" * 60)
    print("✅ Single URL scraping: Tested across multiple website categories")
    print("✅ Batch processing: Demonstrated concurrent URL processing")
    print("✅ Contact extraction: Emails, phones, and social links")
    print("✅ Error handling: Graceful handling of problematic URLs")
    print("✅ Performance: Fast processing with detailed timing")
    print()
    print("📖 Full API Documentation: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print("📊 API Stats: http://localhost:8000/scrape/stats")
    print()
    print("🎉 The Web Scraper API is ready to process hundreds of URLs efficiently!")

if __name__ == "__main__":
    main()
