"""
Main FastAPI application for the Web Scraper API.
"""

import time
from contextlib import asynccontextmanager
from datetime import datetime

from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from .config import get_settings
from .utils.logging import setup_logging, get_logger
from .api import scraper, health
from .models.responses import ErrorResponse

# Global variables for tracking
start_time = time.time()
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    settings = get_settings()
    
    # Setup logging
    setup_logging(
        log_level=settings.log_level,
        log_format=settings.log_format,
        log_file=settings.log_file,
        enable_console=True
    )
    
    logger.info("Starting Web Scraper API", version=settings.api_version)
    
    yield
    
    # Shutdown
    logger.info("Shutting down Web Scraper API")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    settings = get_settings()
    
    app = FastAPI(
        title=settings.api_title,
        version=settings.api_version,
        description=settings.api_description,
        lifespan=lifespan,
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json"
    )
    
    # CORS middleware
    if settings.cors_enabled:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=settings.allowed_origins,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    # Request logging middleware
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        start_time_req = time.time()
        
        # Process request
        response = await call_next(request)
        
        # Log request
        processing_time = time.time() - start_time_req
        logger.info(
            "HTTP request processed",
            method=request.method,
            path=request.url.path,
            status_code=response.status_code,
            processing_time=round(processing_time, 3),
            client_ip=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent", "")
        )
        
        return response
    
    # Exception handlers
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """Handle HTTP exceptions."""
        error_response = ErrorResponse(
            error={
                "code": f"HTTP_{exc.status_code}",
                "message": exc.detail,
                "details": None
            },
            timestamp=datetime.now()
        )
        
        logger.warning(
            "HTTP exception",
            status_code=exc.status_code,
            detail=exc.detail,
            path=request.url.path
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content=error_response.dict()
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle general exceptions."""
        error_response = ErrorResponse(
            error={
                "code": "INTERNAL_ERROR",
                "message": "An internal server error occurred",
                "details": str(exc) if settings.debug else None
            },
            timestamp=datetime.now()
        )
        
        logger.error(
            "Unhandled exception",
            error_type=type(exc).__name__,
            error_message=str(exc),
            path=request.url.path,
            exc_info=True
        )
        
        return JSONResponse(
            status_code=500,
            content=error_response.dict()
        )
    
    # Include routers
    app.include_router(scraper.router, prefix="/scrape", tags=["scraping"])
    app.include_router(health.router, tags=["health"])
    
    # Root endpoint
    @app.get("/", summary="API Information")
    async def root():
        """Get API information and documentation links."""
        settings = get_settings()
        return {
            "name": settings.api_title,
            "version": settings.api_version,
            "description": settings.api_description,
            "docs_url": "/docs",
            "redoc_url": "/redoc",
            "health_url": "/health",
            "endpoints": {
                "single_url": "/scrape/single",
                "batch_urls": "/scrape/batch",
                "health_check": "/health"
            }
        }
    
    return app


# Create the application instance
app = create_app()


def get_uptime() -> int:
    """Get application uptime in seconds."""
    return int(time.time() - start_time)


if __name__ == "__main__":
    import uvicorn
    
    settings = get_settings()
    
    uvicorn.run(
        "app.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.reload,
        workers=1 if settings.reload else settings.api_workers,
        log_level=settings.log_level.lower(),
        access_log=settings.access_log
    )
