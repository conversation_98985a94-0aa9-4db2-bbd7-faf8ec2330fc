#!/bin/bash

# Web Scraper API Setup Script

set -e

echo "🚀 Setting up Web Scraper API..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "   Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    echo "   Visit: https://docs.docker.com/compose/install/"
    exit 1
fi

echo "✅ Docker and Docker Compose are installed"

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p data/results data/temp logs

# Copy environment file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please review and update the .env file with your settings"
else
    echo "✅ .env file already exists"
fi

# Set permissions
echo "🔒 Setting permissions..."
chmod +x scripts/*.sh
chmod 755 data logs

# Build the Docker image
echo "🐳 Building Docker image..."
docker-compose build

echo ""
echo "✅ Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Review and update the .env file if needed"
echo "2. Start the API: ./scripts/start.sh"
echo "3. Visit http://localhost:8000/docs for API documentation"
echo ""
echo "🔧 Available commands:"
echo "  ./scripts/start.sh     - Start the API"
echo "  ./scripts/stop.sh      - Stop the API"
echo "  ./scripts/test.sh      - Run tests"
echo "  ./scripts/logs.sh      - View logs"
echo ""
