# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
API_TITLE="Web Scraper API"
API_VERSION="1.0.0"
API_DESCRIPTION="High-performance web scraper for contact information extraction"

# Security
SECRET_KEY=your-secret-key-here-change-in-production
API_KEY_REQUIRED=false
ALLOWED_ORIGINS=*
CORS_ENABLED=true

# Scraping Configuration
DEFAULT_BATCH_SIZE=50
DEFAULT_MAX_CONCURRENT=10
DEFAULT_TIMEOUT=30
DEFAULT_MAX_PAGES=5
MAX_URLS_PER_BATCH=1000

# Rate Limiting
RATE_LIMIT_SINGLE=60  # requests per minute
RATE_LIMIT_BATCH=10   # jobs per hour
RATE_LIMIT_ENABLED=true

# Database Configuration
DATABASE_URL=sqlite:///./scraper.db
DATABASE_ECHO=false
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis Configuration (Optional - for job queue)
REDIS_URL=redis://localhost:6379/0
REDIS_ENABLED=false

# Job Configuration
MAX_CONCURRENT_JOBS=5
JOB_TIMEOUT=3600  # 1 hour
JOB_CLEANUP_INTERVAL=86400  # 24 hours
JOB_RETENTION_DAYS=7

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=logs/scraper-api.log
LOG_ROTATION=daily
LOG_RETENTION=30

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# Performance Tuning
WORKER_CONNECTIONS=1000
WORKER_TIMEOUT=120
KEEP_ALIVE=2

# Development
DEBUG=false
RELOAD=false
ACCESS_LOG=true

# External Services
USER_AGENT="ScraperAPI/1.0 (+https://your-domain.com/bot)"
RESPECT_ROBOTS_TXT=true
CRAWL_DELAY=1.0

# Storage
RESULTS_STORAGE_PATH=./data/results
TEMP_STORAGE_PATH=./data/temp
MAX_RESULT_SIZE_MB=100
