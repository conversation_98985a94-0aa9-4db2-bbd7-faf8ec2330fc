{"test_categories": {"business_websites": {"description": "Business websites likely to have contact information", "urls": [{"url": "https://www.ycombinator.com/contact", "expected": "emails, possibly phone", "notes": "Tech accelerator contact page"}, {"url": "https://about.gitlab.com/company/contact/", "expected": "emails, social links", "notes": "Software company contact page"}, {"url": "https://www.shopify.com/contact", "expected": "emails, phone, social links", "notes": "E-commerce platform contact"}, {"url": "https://www.atlassian.com/company/contact", "expected": "emails, phone numbers", "notes": "Software company contact page"}]}, "educational_institutions": {"description": "Educational websites with public contact information", "urls": [{"url": "https://www.mit.edu/contact/", "expected": "emails, phone numbers", "notes": "MIT contact page"}, {"url": "https://www.stanford.edu/contact/", "expected": "emails, phone numbers", "notes": "Stanford University contact"}, {"url": "https://www.berkeley.edu/about/contact/", "expected": "emails, phone numbers", "notes": "UC Berkeley contact information"}]}, "government_sites": {"description": "Government websites with public contact information", "urls": [{"url": "https://www.usa.gov/contact/", "expected": "emails, phone numbers", "notes": "US Government contact page"}, {"url": "https://www.nih.gov/about-nih/contact-us", "expected": "emails, phone numbers", "notes": "National Institutes of Health"}, {"url": "https://www.nasa.gov/contact/", "expected": "emails, phone numbers, social links", "notes": "NASA contact information"}]}, "non_profit_organizations": {"description": "Non-profit organizations with contact information", "urls": [{"url": "https://www.redcross.org/about-us/contact-us", "expected": "emails, phone numbers", "notes": "Red Cross contact page"}, {"url": "https://www.unicef.org/contact", "expected": "emails, social links", "notes": "UNICEF contact information"}, {"url": "https://www.wikimedia.org/contact/", "expected": "emails", "notes": "Wikimedia Foundation contact"}]}, "technology_companies": {"description": "Tech companies with support and contact pages", "urls": [{"url": "https://about.twitter.com/en/contact", "expected": "emails, social links", "notes": "Twitter/X contact page"}, {"url": "https://www.mozilla.org/en-US/contact/", "expected": "emails, social links", "notes": "Mozilla contact information"}, {"url": "https://www.docker.com/company/contact/", "expected": "emails, phone numbers", "notes": "Docker company contact"}]}, "small_business_examples": {"description": "Smaller business websites to test various structures", "urls": [{"url": "https://www.example-restaurant.com/contact", "expected": "emails, phone numbers, social links", "notes": "Restaurant contact page (if exists)"}, {"url": "https://httpbin.org/html", "expected": "no contact info", "notes": "Test site with no contact information"}]}, "challenging_cases": {"description": "Sites that test different extraction scenarios", "urls": [{"url": "https://example.com", "expected": "no contact info", "notes": "Simple example site"}, {"url": "https://httpbin.org/json", "expected": "no contact info", "notes": "JSON response test"}, {"url": "https://www.google.com", "expected": "minimal contact info", "notes": "Large site with limited contact info on main page"}]}, "social_media_heavy": {"description": "Sites likely to have social media links", "urls": [{"url": "https://about.instagram.com/contact", "expected": "social links, emails", "notes": "Instagram about page"}, {"url": "https://about.linkedin.com/contact-us", "expected": "emails, social links", "notes": "LinkedIn contact page"}]}, "international_sites": {"description": "International sites to test different formats", "urls": [{"url": "https://www.bbc.com/contact", "expected": "emails, phone numbers", "notes": "BBC contact information"}, {"url": "https://www.cbc.ca/contact/", "expected": "emails, phone numbers", "notes": "CBC (Canadian) contact"}]}}, "batch_test_sets": {"quick_demo": {"description": "Quick 5-URL demo set", "urls": ["https://www.mit.edu/contact/", "https://www.ycombinator.com/contact", "https://www.mozilla.org/en-US/contact/", "https://example.com", "https://httpbin.org/html"]}, "comprehensive_test": {"description": "Comprehensive 15-URL test set", "urls": ["https://www.mit.edu/contact/", "https://www.ycombinator.com/contact", "https://about.gitlab.com/company/contact/", "https://www.mozilla.org/en-US/contact/", "https://www.usa.gov/contact/", "https://www.redcross.org/about-us/contact-us", "https://www.docker.com/company/contact/", "https://www.bbc.com/contact", "https://www.wikimedia.org/contact/", "https://www.nasa.gov/contact/", "https://example.com", "https://httpbin.org/html", "https://httpbin.org/json", "https://www.google.com", "https://about.twitter.com/en/contact"]}, "stress_test": {"description": "Larger set for performance testing (25 URLs)", "urls": ["https://www.mit.edu/contact/", "https://www.stanford.edu/contact/", "https://www.berkeley.edu/about/contact/", "https://www.ycombinator.com/contact", "https://about.gitlab.com/company/contact/", "https://www.shopify.com/contact", "https://www.atlassian.com/company/contact", "https://www.mozilla.org/en-US/contact/", "https://www.docker.com/company/contact/", "https://www.usa.gov/contact/", "https://www.nih.gov/about-nih/contact-us", "https://www.nasa.gov/contact/", "https://www.redcross.org/about-us/contact-us", "https://www.unicef.org/contact", "https://www.wikimedia.org/contact/", "https://about.twitter.com/en/contact", "https://about.instagram.com/contact", "https://about.linkedin.com/contact-us", "https://www.bbc.com/contact", "https://www.cbc.ca/contact/", "https://example.com", "https://httpbin.org/html", "https://httpbin.org/json", "https://www.google.com", "https://httpbin.org/status/200"]}}}