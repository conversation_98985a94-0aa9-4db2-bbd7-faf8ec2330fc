# 🚀 Web Scraper API Test URLs & Demonstration

This document provides a comprehensive list of test URLs to demonstrate the Docker-based Web Scraper API's capabilities across different website types and contact extraction scenarios.

## 📋 **Quick Test URLs for Immediate Demonstration**

### **Basic Functionality Tests**
```bash
# Test 1: Simple HTML page (baseline)
curl -X POST "http://localhost:8000/scrape/single" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://httpbin.org/html"}'

# Test 2: Example site (error handling)
curl -X POST "http://localhost:8000/scrape/single" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com"}'

# Test 3: IANA contact page (likely to have emails)
curl -X POST "http://localhost:8000/scrape/single" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://www.iana.org/contact"}'
```

### **Batch Processing Test**
```bash
curl -X POST "http://localhost:8000/scrape/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "urls": [
      "https://httpbin.org/html",
      "https://example.com",
      "https://www.iana.org/contact",
      "https://www.rfc-editor.org/contact/",
      "https://www.w3.org/Consortium/contact"
    ],
    "batch_options": {
      "batch_size": 3,
      "max_concurrent": 2
    }
  }'
```

## 🎯 **Categorized Test URLs**

### **1. Educational Institutions** (High Success Rate)
- **MIT Contact**: `https://www.mit.edu/contact/`
  - Expected: Email addresses, phone numbers
  - Notes: Well-structured contact page

- **Stanford Contact**: `https://www.stanford.edu/contact/`
  - Expected: Email addresses, phone numbers
  - Notes: University contact information

- **UC Berkeley**: `https://www.berkeley.edu/about/contact/`
  - Expected: Email addresses, phone numbers
  - Notes: Public university contact page

### **2. Technology Companies** (Moderate Success Rate)
- **Y Combinator**: `https://www.ycombinator.com/contact`
  - Expected: Email addresses
  - Notes: Tech accelerator contact page

- **GitLab**: `https://about.gitlab.com/company/contact/`
  - Expected: Email addresses, social links
  - Notes: Software company contact page

- **Mozilla**: `https://www.mozilla.org/en-US/contact/`
  - Expected: Email addresses, social links
  - Notes: Open source organization

- **Docker**: `https://www.docker.com/company/contact/`
  - Expected: Email addresses, phone numbers
  - Notes: Container platform company

### **3. Government Sites** (High Success Rate)
- **USA.gov**: `https://www.usa.gov/contact/`
  - Expected: Email addresses, phone numbers
  - Notes: US Government contact page

- **NASA**: `https://www.nasa.gov/contact/`
  - Expected: Email addresses, phone numbers, social links
  - Notes: Space agency contact information

- **NIH**: `https://www.nih.gov/about-nih/contact-us`
  - Expected: Email addresses, phone numbers
  - Notes: National Institutes of Health

### **4. Non-Profit Organizations** (Moderate Success Rate)
- **Red Cross**: `https://www.redcross.org/about-us/contact-us`
  - Expected: Email addresses, phone numbers
  - Notes: Humanitarian organization

- **UNICEF**: `https://www.unicef.org/contact`
  - Expected: Email addresses, social links
  - Notes: International children's organization

- **Wikimedia**: `https://www.wikimedia.org/contact/`
  - Expected: Email addresses
  - Notes: Wikipedia foundation

### **5. International Sites** (Variable Success Rate)
- **BBC**: `https://www.bbc.com/contact`
  - Expected: Email addresses, phone numbers
  - Notes: British broadcasting corporation

- **CBC**: `https://www.cbc.ca/contact/`
  - Expected: Email addresses, phone numbers
  - Notes: Canadian broadcasting corporation

### **6. Social Media & Tech Platforms** (Low-Moderate Success Rate)
- **Twitter/X**: `https://about.twitter.com/en/contact`
  - Expected: Email addresses, social links
  - Notes: Social media platform

- **Instagram**: `https://about.instagram.com/contact`
  - Expected: Social links, emails
  - Notes: Photo sharing platform

- **LinkedIn**: `https://about.linkedin.com/contact-us`
  - Expected: Email addresses, social links
  - Notes: Professional networking platform

### **7. Challenging/Edge Cases** (Low Success Rate - Good for Testing)
- **Google**: `https://www.google.com`
  - Expected: Minimal contact info
  - Notes: Large site with limited contact info on main page

- **JSON Response**: `https://httpbin.org/json`
  - Expected: No contact info
  - Notes: JSON response test

- **Status Page**: `https://httpbin.org/status/200`
  - Expected: No contact info
  - Notes: HTTP status test

## 🚀 **Demonstration Scripts**

### **Quick 5-URL Demo**
```python
# Use the quick_demo.py script
python quick_demo.py
```

### **Comprehensive Test**
```python
# Use the full demonstration script
python demo_scraper_capabilities.py
```

### **Custom Batch Test**
```bash
# Test with your own URLs
curl -X POST "http://localhost:8000/scrape/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "urls": [
      "YOUR_URL_1",
      "YOUR_URL_2",
      "YOUR_URL_3"
    ],
    "batch_options": {
      "batch_size": 10,
      "max_concurrent": 5
    },
    "scraping_options": {
      "timeout": 15,
      "max_pages": 2
    }
  }'
```

## 📊 **Expected Results by Category**

| Category | Success Rate | Typical Findings |
|----------|-------------|------------------|
| Educational | 80-90% | Emails, phones, addresses |
| Government | 85-95% | Emails, phones, official contacts |
| Non-Profit | 70-80% | Emails, donation info, social links |
| Tech Companies | 60-70% | Emails, support contacts, social |
| International | 60-80% | Varies by region and site structure |
| Social Media | 40-60% | Social links, limited direct contact |
| Edge Cases | 10-20% | Error handling, no contacts |

## 🎯 **Performance Expectations**

- **Single URL**: 2-15 seconds per URL (depending on complexity)
- **Batch Processing**: 50-100 URLs per minute with default settings
- **Concurrent Processing**: 5-10 URLs simultaneously
- **Memory Usage**: ~100-500MB depending on batch size

## 🔧 **API Endpoints for Testing**

- **Health Check**: `GET http://localhost:8000/health`
- **Single URL**: `POST http://localhost:8000/scrape/single`
- **Batch URLs**: `POST http://localhost:8000/scrape/batch`
- **Stats**: `GET http://localhost:8000/scrape/stats`
- **Documentation**: `GET http://localhost:8000/docs`

## 🎉 **Demonstration Commands**

### **Start the API**
```bash
cd scraper-api
docker-compose up -d
```

### **Check Health**
```bash
curl http://localhost:8000/health
```

### **Run Quick Demo**
```bash
python quick_demo.py
```

### **View Documentation**
Open browser to: `http://localhost:8000/docs`

## 📝 **Notes for Testing**

1. **Success Rates**: Vary based on website structure and anti-bot measures
2. **Rate Limiting**: Some sites may block rapid requests
3. **Dynamic Content**: JavaScript-heavy sites may have limited extraction
4. **Contact Types**: Focus on publicly available contact information
5. **Error Handling**: API gracefully handles failed requests and timeouts

## 🚀 **Ready for Production**

The Web Scraper API is designed to handle:
- ✅ **Hundreds of URLs** in batch processing
- ✅ **Multiple website types** with adaptive strategies
- ✅ **Robust error handling** for problematic sites
- ✅ **Concurrent processing** for optimal performance
- ✅ **Comprehensive logging** for monitoring and debugging

**The scraper is ready to process your URL lists efficiently!** 🎯
