"""
Pydantic models for the Web Scraper API.
"""

from .requests import (
    SingleUrlRequest,
    BatchUrlRequest,
    ScrapingOptions
)
from .responses import (
    SingleUrlResponse,
    BatchUrlResponse,
    ContactData,
    EmailData,
    PhoneData,
    SocialMediaData,
    ScrapingMetadata,
    ErrorResponse,
    HealthResponse
)
from .jobs import (
    JobStatus,
    JobProgress,
    JobResult
)

__all__ = [
    # Request models
    "SingleUrlRequest",
    "BatchUrlRequest", 
    "ScrapingOptions",
    
    # Response models
    "SingleUrlResponse",
    "BatchUrlResponse",
    "ContactData",
    "EmailData",
    "PhoneData", 
    "SocialMediaData",
    "ScrapingMetadata",
    "ErrorResponse",
    "HealthResponse",
    
    # Job models
    "JobStatus",
    "JobProgress",
    "JobResult"
]
