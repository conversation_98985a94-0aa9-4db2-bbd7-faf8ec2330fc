version: '3.8'

services:
  # Production API service with multiple workers
  scraper-api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    environment:
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - API_WORKERS=4
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
      - DATABASE_URL=sqlite:///./data/scraper.db
      - DEFAULT_BATCH_SIZE=50
      - DEFAULT_MAX_CONCURRENT=10
      - DEFAULT_TIMEOUT=30
      - MAX_URLS_PER_BATCH=1000
      - RATE_LIMIT_ENABLED=true
      - RATE_LIMIT_SINGLE=60
      - RATE_LIMIT_BATCH=10
      - CORS_ENABLED=true
      - ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
      - ENABLE_METRICS=true
      - DEBUG=false
    volumes:
      - api_data:/app/data
      - api_logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - scraper-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis for job queue and caching
  redis:
    image: redis:7-alpine
    container_name: scraper-redis-prod
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - scraper-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy with SSL
  nginx:
    image: nginx:alpine
    container_name: scraper-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - scraper-api
    restart: unless-stopped
    networks:
      - scraper-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for metrics (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: scraper-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - scraper-network

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: scraper-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    restart: unless-stopped
    networks:
      - scraper-network

networks:
  scraper-network:
    driver: bridge

volumes:
  redis_data:
  api_data:
  api_logs:
  nginx_logs:
  prometheus_data:
  grafana_data:
