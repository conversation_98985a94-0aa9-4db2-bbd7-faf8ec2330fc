"""
Validation utilities for URLs, emails, and other data.
"""

import re
from typing import List
from urllib.parse import urlparse

import validators


def validate_url(url: str) -> bool:
    """
    Validate a single URL.
    
    Args:
        url: URL to validate
        
    Returns:
        True if URL is valid, False otherwise
    """
    if not url or not isinstance(url, str):
        return False
    
    # Basic format check
    if not url.startswith(('http://', 'https://')):
        return False
    
    # Use validators library for comprehensive validation
    try:
        return validators.url(url) is True
    except:
        return False


def validate_urls(urls: List[str]) -> tuple[List[str], List[str]]:
    """
    Validate a list of URLs.
    
    Args:
        urls: List of URLs to validate
        
    Returns:
        Tuple of (valid_urls, invalid_urls)
    """
    valid_urls = []
    invalid_urls = []
    
    for url in urls:
        if validate_url(url):
            valid_urls.append(url)
        else:
            invalid_urls.append(url)
    
    return valid_urls, invalid_urls


def validate_email(email: str) -> bool:
    """
    Validate an email address.
    
    Args:
        email: Email address to validate
        
    Returns:
        True if email is valid, False otherwise
    """
    if not email or not isinstance(email, str):
        return False
    
    # Length check
    if len(email) < 5 or len(email) > 254:
        return False
    
    # Basic format check
    if email.count('@') != 1:
        return False
    
    # Regex validation
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(pattern, email):
        return False
    
    # Use validators library
    try:
        return validators.email(email) is True
    except:
        return False


def validate_phone(phone: str) -> bool:
    """
    Validate a phone number.
    
    Args:
        phone: Phone number to validate
        
    Returns:
        True if phone is valid, False otherwise
    """
    if not phone or not isinstance(phone, str):
        return False
    
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone)
    
    # Check length (10-15 digits for international numbers)
    if not (10 <= len(digits_only) <= 15):
        return False
    
    # Avoid obviously invalid patterns
    invalid_patterns = [
        '0000000000', '1111111111', '2222222222', '3333333333',
        '4444444444', '5555555555', '6666666666', '7777777777',
        '8888888888', '9999999999', '1234567890', '0123456789'
    ]
    
    if digits_only in invalid_patterns:
        return False
    
    return True


def validate_batch_size(batch_size: int, max_allowed: int = 1000) -> bool:
    """
    Validate batch size parameter.
    
    Args:
        batch_size: Batch size to validate
        max_allowed: Maximum allowed batch size
        
    Returns:
        True if batch size is valid, False otherwise
    """
    return isinstance(batch_size, int) and 1 <= batch_size <= max_allowed


def validate_timeout(timeout: int, max_allowed: int = 300) -> bool:
    """
    Validate timeout parameter.
    
    Args:
        timeout: Timeout to validate in seconds
        max_allowed: Maximum allowed timeout
        
    Returns:
        True if timeout is valid, False otherwise
    """
    return isinstance(timeout, int) and 1 <= timeout <= max_allowed


def validate_max_pages(max_pages: int, max_allowed: int = 20) -> bool:
    """
    Validate max_pages parameter.
    
    Args:
        max_pages: Max pages to validate
        max_allowed: Maximum allowed pages
        
    Returns:
        True if max_pages is valid, False otherwise
    """
    return isinstance(max_pages, int) and 1 <= max_pages <= max_allowed


def validate_max_concurrent(max_concurrent: int, max_allowed: int = 50) -> bool:
    """
    Validate max_concurrent parameter.
    
    Args:
        max_concurrent: Max concurrent requests to validate
        max_allowed: Maximum allowed concurrent requests
        
    Returns:
        True if max_concurrent is valid, False otherwise
    """
    return isinstance(max_concurrent, int) and 1 <= max_concurrent <= max_allowed


def sanitize_url(url: str) -> str:
    """
    Sanitize a URL by removing unnecessary parts and normalizing.
    
    Args:
        url: URL to sanitize
        
    Returns:
        Sanitized URL
    """
    if not url:
        return url
    
    # Strip whitespace
    url = url.strip()
    
    # Add protocol if missing
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url
    
    # Parse and reconstruct to normalize
    try:
        parsed = urlparse(url)
        # Remove fragment and query parameters for consistency
        sanitized = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
        # Remove trailing slash unless it's the root
        if sanitized.endswith('/') and len(parsed.path) > 1:
            sanitized = sanitized[:-1]
        return sanitized
    except:
        return url


def extract_domain(url: str) -> str:
    """
    Extract domain from URL.
    
    Args:
        url: URL to extract domain from
        
    Returns:
        Domain name or empty string if invalid
    """
    try:
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        # Remove www. prefix
        if domain.startswith('www.'):
            domain = domain[4:]
        return domain
    except:
        return ""
