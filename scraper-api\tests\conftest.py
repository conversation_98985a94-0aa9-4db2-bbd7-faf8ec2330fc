"""
Pytest configuration and fixtures for the Web Scraper API tests.
"""

import asyncio
import pytest
import sys
import os
from typing import Async<PERSON>enerator, Generator
from fastapi.testclient import TestClient

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'app'))

from app.main import create_app
from app.core.scraper import WebScraperEngine
from app.core.extractor import ContactExtractor
from app.config import get_settings


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def app():
    """Create a FastAPI application for testing."""
    return create_app()


@pytest.fixture
def client(app):
    """Create a test client for the FastAPI application."""
    with TestClient(app) as test_client:
        yield test_client


@pytest.fixture
async def scraper_engine():
    """Create a WebScraperEngine instance for testing."""
    engine = WebScraperEngine(
        batch_size=2,
        max_concurrent=1,
        default_timeout=10,
        default_max_pages=2
    )
    return engine


@pytest.fixture
async def contact_extractor():
    """Create a ContactExtractor instance for testing."""
    extractor = ContactExtractor()
    return extractor


@pytest.fixture
def test_urls():
    """Provide test URLs for scraping tests."""
    return {
        "valid": [
            "https://httpbin.org",
            "https://example.com",
            "https://www.python.org"
        ],
        "invalid": [
            "not-a-url",
            "ftp://example.com",
            "http://",
            ""
        ],
        "timeout": [
            "https://httpstat.us/200?sleep=30000"  # Will timeout
        ],
        "error": [
            "https://httpstat.us/404",
            "https://httpstat.us/500"
        ]
    }


@pytest.fixture
def sample_html():
    """Provide sample HTML content for testing extraction."""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test Company</title>
    </head>
    <body>
        <div class="contact">
            <p>Email us at: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p>Call us: <span class="phone">******-123-4567</span></p>
            <p>Follow us: <a href="https://instagram.com/testcompany">@testcompany</a></p>
        </div>
        <footer>
            <p>Alternative email: <EMAIL></p>
            <p>Phone: (*************</p>
            <p>Social: <a href="https://facebook.com/testcompany">Facebook</a></p>
        </footer>
    </body>
    </html>
    """


@pytest.fixture
def sample_contact_data():
    """Provide sample contact data for testing."""
    return {
        "email": {
            "email": "<EMAIL>",
            "confidence": 0.95
        },
        "phone": {
            "phone": "******-123-4567",
            "confidence": 0.90
        },
        "social_media": {
            "platform": "instagram",
            "url": "https://instagram.com/example",
            "handle": "@example",
            "confidence": 0.85
        }
    }


@pytest.fixture
def sample_metadata():
    """Provide sample metadata for testing."""
    return {
        "pages_checked": 3,
        "pages_available": 5,
        "pages_with_content": 2,
        "efficiency": "3/5",
        "website_type": "business"
    }


@pytest.fixture
def settings():
    """Get application settings."""
    return get_settings()


# Async test utilities
@pytest.fixture
async def async_client(app):
    """Create an async test client."""
    from httpx import AsyncClient
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


# Mock data for testing
@pytest.fixture
def mock_successful_scrape_result():
    """Mock successful scrape result."""
    return {
        "success": True,
        "url": "https://example.com",
        "timestamp": "2025-01-26T10:30:00Z",
        "processing_time": 12.5,
        "data": {
            "email": {
                "email": "<EMAIL>",
                "confidence": 0.95
            },
            "phone": {
                "phone": "******-123-4567",
                "confidence": 0.90
            },
            "social_media": {
                "platform": "instagram",
                "url": "https://instagram.com/example",
                "handle": "@example",
                "confidence": 0.85
            }
        },
        "metadata": {
            "pages_checked": 3,
            "pages_available": 5,
            "pages_with_content": 2,
            "efficiency": "3/5",
            "website_type": "business"
        }
    }


@pytest.fixture
def mock_failed_scrape_result():
    """Mock failed scrape result."""
    return {
        "success": False,
        "url": "https://example.com",
        "timestamp": "2025-01-26T10:30:00Z",
        "processing_time": 5.0,
        "error": {
            "code": "TIMEOUT_ERROR",
            "message": "Request timed out",
            "details": "The request took longer than 30 seconds"
        }
    }


@pytest.fixture
def mock_batch_result():
    """Mock batch processing result."""
    return {
        "success": True,
        "total_processing_time": 45.2,
        "timestamp": "2025-01-26T10:35:00Z",
        "results": [
            {
                "success": True,
                "url": "https://example1.com",
                "timestamp": "2025-01-26T10:30:00Z",
                "processing_time": 12.5,
                "data": {
                    "email": {
                        "email": "<EMAIL>",
                        "confidence": 0.95
                    }
                },
                "metadata": {
                    "pages_checked": 2,
                    "pages_available": 3,
                    "pages_with_content": 1,
                    "efficiency": "2/3",
                    "website_type": "business"
                }
            },
            {
                "success": False,
                "url": "https://example2.com",
                "timestamp": "2025-01-26T10:32:00Z",
                "processing_time": 5.0,
                "error": {
                    "code": "TIMEOUT_ERROR",
                    "message": "Request timed out"
                }
            }
        ],
        "summary": {
            "total_urls": 2,
            "valid_urls": 2,
            "invalid_urls": 0,
            "successful": 1,
            "failed": 1,
            "emails_found": 1,
            "phones_found": 0,
            "socials_found": 0,
            "avg_processing_time": 8.75
        }
    }
