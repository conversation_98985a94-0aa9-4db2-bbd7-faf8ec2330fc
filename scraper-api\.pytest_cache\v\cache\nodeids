["tests/test_api_endpoints.py::TestCORS::test_cors_headers", "tests/test_api_endpoints.py::TestCORS::test_preflight_request", "tests/test_api_endpoints.py::TestErrorHandling::test_404_endpoint", "tests/test_api_endpoints.py::TestErrorHandling::test_405_method_not_allowed", "tests/test_api_endpoints.py::TestErrorHandling::test_invalid_json", "tests/test_api_endpoints.py::TestHealthEndpoints::test_health_check", "tests/test_api_endpoints.py::TestHealthEndpoints::test_liveness_check", "tests/test_api_endpoints.py::TestHealthEndpoints::test_readiness_check", "tests/test_api_endpoints.py::TestRootEndpoint::test_root_endpoint", "tests/test_api_endpoints.py::TestScraperEndpoints::test_batch_urls_empty_list", "tests/test_api_endpoints.py::TestScraperEndpoints::test_batch_urls_invalid_urls", "tests/test_api_endpoints.py::TestScraperEndpoints::test_batch_urls_minimal_request", "tests/test_api_endpoints.py::TestScraperEndpoints::test_batch_urls_too_many_urls", "tests/test_api_endpoints.py::TestScraperEndpoints::test_batch_urls_valid_request", "tests/test_api_endpoints.py::TestScraperEndpoints::test_scraper_stats", "tests/test_api_endpoints.py::TestScraperEndpoints::test_single_url_invalid_options", "tests/test_api_endpoints.py::TestScraperEndpoints::test_single_url_invalid_url", "tests/test_api_endpoints.py::TestScraperEndpoints::test_single_url_minimal_request", "tests/test_api_endpoints.py::TestScraperEndpoints::test_single_url_missing_url", "tests/test_api_endpoints.py::TestScraperEndpoints::test_single_url_valid_request", "tests/test_core_functionality.py::TestContactExtractor::test_email_validation", "tests/test_core_functionality.py::TestContactExtractor::test_extract_from_html", "tests/test_core_functionality.py::TestContactExtractor::test_extractor_initialization", "tests/test_core_functionality.py::TestContactExtractor::test_phone_validation", "tests/test_core_functionality.py::TestContactExtractor::test_social_url_validation", "tests/test_core_functionality.py::TestContactExtractor::test_website_type_detection", "tests/test_core_functionality.py::TestErrorHandling::test_http_error_handling", "tests/test_core_functionality.py::TestErrorHandling::test_network_error_handling", "tests/test_core_functionality.py::TestErrorHandling::test_timeout_handling", "tests/test_core_functionality.py::TestValidationUtils::test_email_validation", "tests/test_core_functionality.py::TestValidationUtils::test_phone_validation", "tests/test_core_functionality.py::TestValidationUtils::test_url_validation", "tests/test_core_functionality.py::TestWebScraperEngine::test_batch_url_scraping_empty_list", "tests/test_core_functionality.py::TestWebScraperEngine::test_batch_url_scraping_mixed_validity", "tests/test_core_functionality.py::TestWebScraperEngine::test_batch_url_scraping_success", "tests/test_core_functionality.py::TestWebScraperEngine::test_engine_initialization", "tests/test_core_functionality.py::TestWebScraperEngine::test_engine_stats", "tests/test_core_functionality.py::TestWebScraperEngine::test_single_url_scraping_invalid_url", "tests/test_core_functionality.py::TestWebScraperEngine::test_single_url_scraping_success", "tests/test_models.py::TestModelSerialization::test_batch_url_request_json", "tests/test_models.py::TestModelSerialization::test_error_response_json", "tests/test_models.py::TestModelSerialization::test_single_url_request_json", "tests/test_models.py::TestModelSerialization::test_single_url_response_json", "tests/test_models.py::TestRequestModels::test_batch_url_request_empty_urls", "tests/test_models.py::TestRequestModels::test_batch_url_request_invalid_urls", "tests/test_models.py::TestRequestModels::test_batch_url_request_too_many_urls", "tests/test_models.py::TestRequestModels::test_batch_url_request_valid", "tests/test_models.py::TestRequestModels::test_scraping_options_defaults", "tests/test_models.py::TestRequestModels::test_scraping_options_invalid_max_pages", "tests/test_models.py::TestRequestModels::test_scraping_options_invalid_timeout", "tests/test_models.py::TestRequestModels::test_scraping_options_valid", "tests/test_models.py::TestRequestModels::test_single_url_request_invalid_url", "tests/test_models.py::TestRequestModels::test_single_url_request_minimal", "tests/test_models.py::TestRequestModels::test_single_url_request_valid", "tests/test_models.py::TestResponseModels::test_contact_data_empty", "tests/test_models.py::TestResponseModels::test_contact_data_full", "tests/test_models.py::TestResponseModels::test_email_data_invalid_confidence", "tests/test_models.py::TestResponseModels::test_email_data_valid", "tests/test_models.py::TestResponseModels::test_error_response", "tests/test_models.py::TestResponseModels::test_health_response", "tests/test_models.py::TestResponseModels::test_health_response_with_system_info", "tests/test_models.py::TestResponseModels::test_phone_data_valid", "tests/test_models.py::TestResponseModels::test_single_url_response_failure", "tests/test_models.py::TestResponseModels::test_single_url_response_success", "tests/test_models.py::TestResponseModels::test_social_media_data_minimal", "tests/test_models.py::TestResponseModels::test_social_media_data_valid"]