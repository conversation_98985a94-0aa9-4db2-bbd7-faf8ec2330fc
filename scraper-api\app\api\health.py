"""
Health check API endpoints.
"""

import psutil
import time
from datetime import datetime

from fastapi import APIRouter

from ..config import get_settings
from ..models.responses import HealthResponse, SystemInfo
from ..utils.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)

# Track start time for uptime calculation
start_time = time.time()


@router.get(
    "/health",
    response_model=HealthResponse,
    summary="Health Check",
    description="Get API health status and system information"
)
async def health_check() -> HealthResponse:
    """
    Get the health status of the API.
    
    Returns system information including:
    - API status and uptime
    - System resource usage (CPU, memory, disk)
    - Active job count
    - Version information
    """
    try:
        settings = get_settings()
        
        # Calculate uptime
        uptime = int(time.time() - start_time)
        
        # Get system information
        system_info = None
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            system_info = SystemInfo(
                cpu_usage=round(cpu_percent, 1),
                memory_usage=round(memory.percent, 1),
                disk_usage=round(disk.percent, 1)
            )
        except Exception as e:
            logger.warning("Failed to get system info", error=str(e))
        
        # Determine health status
        status = "healthy"
        if system_info:
            if system_info.cpu_usage > 90 or system_info.memory_usage > 90:
                status = "degraded"
            elif system_info.cpu_usage > 95 or system_info.memory_usage > 95:
                status = "unhealthy"
        
        return HealthResponse(
            status=status,
            timestamp=datetime.now(),
            version=settings.api_version,
            uptime=uptime,
            active_jobs=0,  # TODO: Implement job tracking
            system=system_info
        )
    
    except Exception as e:
        logger.error("Health check failed", error=str(e), exc_info=True)
        
        return HealthResponse(
            status="unhealthy",
            timestamp=datetime.now(),
            version=settings.api_version,
            uptime=int(time.time() - start_time),
            active_jobs=0
        )


@router.get(
    "/ready",
    summary="Readiness Check",
    description="Check if the API is ready to accept requests"
)
async def readiness_check():
    """
    Check if the API is ready to accept requests.
    
    This is a simple endpoint that returns 200 OK if the API is ready.
    Useful for Kubernetes readiness probes.
    """
    return {
        "status": "ready",
        "timestamp": datetime.now().isoformat()
    }


@router.get(
    "/live",
    summary="Liveness Check", 
    description="Check if the API is alive"
)
async def liveness_check():
    """
    Check if the API is alive.
    
    This is a simple endpoint that returns 200 OK if the API is running.
    Useful for Kubernetes liveness probes.
    """
    return {
        "status": "alive",
        "timestamp": datetime.now().isoformat()
    }
