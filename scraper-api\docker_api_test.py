#!/usr/bin/env python3
"""
Docker API Test Script
Tests the Web Scraper API running in Docker container.
"""

import requests
import json
import time
from typing import Dict, Any

API_BASE_URL = "http://localhost:8000"

def test_health_endpoint():
    """Test the health endpoint."""
    print("🔍 Testing Health Endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        response.raise_for_status()
        data = response.json()
        print(f"✅ Health Status: {data['status']}")
        print(f"   Version: {data['version']}")
        print(f"   Uptime: {data['uptime']} seconds")
        print(f"   System CPU: {data['system']['cpu_usage']}%")
        print(f"   System Memory: {data['system']['memory_usage']}%")
        return True
    except Exception as e:
        print(f"❌ Health endpoint failed: {e}")
        return False

def test_root_endpoint():
    """Test the root endpoint."""
    print("\n🔍 Testing Root Endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/")
        response.raise_for_status()
        data = response.json()
        print(f"✅ API Name: {data['name']}")
        print(f"   Description: {data['description']}")
        print(f"   Documentation: {data['docs_url']}")
        return True
    except Exception as e:
        print(f"❌ Root endpoint failed: {e}")
        return False

def test_single_url_scraping():
    """Test single URL scraping."""
    print("\n🔍 Testing Single URL Scraping...")
    try:
        payload = {
            "url": "https://httpbin.org/html",
            "options": {
                "timeout": 10,
                "max_pages": 1
            }
        }
        
        response = requests.post(
            f"{API_BASE_URL}/scrape/single",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        data = response.json()
        
        print(f"✅ Single URL Scraping Response:")
        print(f"   URL: {data['url']}")
        print(f"   Success: {data['success']}")
        print(f"   Processing Time: {data['processing_time']:.2f}s")
        
        if data['success'] and data['data']:
            contacts = data['data']
            print(f"   Emails Found: {len(contacts.get('emails', []))}")
            print(f"   Phones Found: {len(contacts.get('phones', []))}")
            print(f"   Social Links Found: {len(contacts.get('social_links', []))}")
        
        return True
    except Exception as e:
        print(f"❌ Single URL scraping failed: {e}")
        return False

def test_batch_url_scraping():
    """Test batch URL scraping."""
    print("\n🔍 Testing Batch URL Scraping...")
    try:
        payload = {
            "urls": [
                "https://httpbin.org/html",
                "https://example.com"
            ],
            "batch_options": {
                "batch_size": 2,
                "max_concurrent": 2
            },
            "scraping_options": {
                "timeout": 10,
                "max_pages": 1
            }
        }
        
        response = requests.post(
            f"{API_BASE_URL}/scrape/batch",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        data = response.json()
        
        print(f"✅ Batch URL Scraping Response:")
        print(f"   Success: {data['success']}")
        print(f"   Total Processing Time: {data['total_processing_time']:.2f}s")
        print(f"   Results Count: {len(data['results'])}")
        
        for i, result in enumerate(data['results']):
            print(f"   Result {i+1}: {result['url']} - {'✅' if result['success'] else '❌'}")
        
        return True
    except Exception as e:
        print(f"❌ Batch URL scraping failed: {e}")
        return False

def test_stats_endpoint():
    """Test the stats endpoint."""
    print("\n🔍 Testing Stats Endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/scrape/stats")
        response.raise_for_status()
        data = response.json()
        
        print(f"✅ Stats Response:")
        stats = data['stats']
        print(f"   Batch Size: {stats['batch_size']}")
        print(f"   Max Concurrent: {stats['max_concurrent']}")
        print(f"   Default Timeout: {stats['default_timeout']}s")
        print(f"   Engine Status: {stats['engine_status']}")
        
        return True
    except Exception as e:
        print(f"❌ Stats endpoint failed: {e}")
        return False

def main():
    """Run all API tests."""
    print("🚀 Docker Web Scraper API Test Suite")
    print("=" * 50)
    
    # Check if API is accessible
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code != 200:
            print("❌ API is not accessible. Make sure the Docker container is running:")
            print("   docker run -d -p 8000:8000 --name scraper-api-test scraper-api")
            return
    except requests.exceptions.RequestException:
        print("❌ API is not accessible. Make sure the Docker container is running:")
        print("   docker run -d -p 8000:8000 --name scraper-api-test scraper-api")
        return
    
    # Run tests
    tests = [
        test_health_endpoint,
        test_root_endpoint,
        test_single_url_scraping,
        test_batch_url_scraping,
        test_stats_endpoint
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        time.sleep(1)  # Brief pause between tests
    
    print("\n" + "=" * 50)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! The Docker Web Scraper API is working perfectly!")
        print("\n📖 API Documentation: http://localhost:8000/docs")
        print("🔍 Health Check: http://localhost:8000/health")
        print("📊 API Stats: http://localhost:8000/scrape/stats")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
