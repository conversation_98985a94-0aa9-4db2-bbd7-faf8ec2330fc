# Web Scraper API Design Specification

## Overview
A deployable Docker-based web scraper API for extracting contact information from websites. Supports both single URL processing and batch processing of hundreds of URLs.

## Technology Stack
- **Framework**: FastAPI (Python)
- **Async Processing**: AsyncIO + Background Tasks
- **Data Validation**: Pydantic
- **Job Storage**: SQLite (simple) or Redis (production)
- **Deployment**: Docker + Docker Compose
- **Core Engine**: Extracted PerfectContactExtractor

## API Endpoints

### 1. Single URL Processing
```
POST /scrape/single
```

**Request:**
```json
{
  "url": "https://example.com",
  "options": {
    "max_pages": 5,
    "timeout": 30,
    "include_social": true,
    "include_phone": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "url": "https://example.com",
  "timestamp": "2025-01-26T10:30:00Z",
  "processing_time": 12.5,
  "data": {
    "email": {
      "email": "<EMAIL>",
      "confidence": 0.95
    },
    "phone": {
      "phone": "******-123-4567",
      "confidence": 0.90
    },
    "social_media": {
      "platform": "instagram",
      "handle": "@example",
      "url": "https://instagram.com/example",
      "confidence": 0.85
    }
  },
  "metadata": {
    "pages_checked": 3,
    "pages_available": 5,
    "pages_with_content": 2,
    "efficiency": "3/5",
    "website_type": "business"
  }
}
```

### 2. Batch URL Processing
```
POST /scrape/batch
```

**Request:**
```json
{
  "urls": [
    "https://example1.com",
    "https://example2.com",
    "https://example3.com"
  ],
  "options": {
    "batch_size": 50,
    "max_concurrent": 10,
    "max_pages_per_url": 5,
    "timeout": 30,
    "include_social": true,
    "include_phone": true
  },
  "callback_url": "https://your-app.com/webhook" // Optional
}
```

**Response:**
```json
{
  "success": true,
  "job_id": "job_abc123def456",
  "status": "queued",
  "total_urls": 3,
  "estimated_time": 45,
  "created_at": "2025-01-26T10:30:00Z",
  "status_url": "/jobs/job_abc123def456",
  "results_url": "/jobs/job_abc123def456/results"
}
```

### 3. Job Status
```
GET /jobs/{job_id}
```

**Response:**
```json
{
  "job_id": "job_abc123def456",
  "status": "processing", // queued, processing, completed, failed
  "progress": {
    "total_urls": 100,
    "processed_urls": 45,
    "successful": 42,
    "failed": 3,
    "percentage": 45.0
  },
  "created_at": "2025-01-26T10:30:00Z",
  "started_at": "2025-01-26T10:31:00Z",
  "estimated_completion": "2025-01-26T10:45:00Z",
  "processing_time": 840.5
}
```

### 4. Job Results
```
GET /jobs/{job_id}/results
```

**Query Parameters:**
- `format`: json (default), csv
- `page`: 1 (default)
- `limit`: 100 (default)
- `filter`: success, failed, all (default)

**Response (JSON):**
```json
{
  "job_id": "job_abc123def456",
  "status": "completed",
  "total_results": 100,
  "page": 1,
  "limit": 100,
  "results": [
    {
      "url": "https://example1.com",
      "success": true,
      "timestamp": "2025-01-26T10:32:15Z",
      "processing_time": 8.2,
      "data": {
        "email": {...},
        "phone": {...},
        "social_media": {...}
      },
      "metadata": {...}
    }
  ],
  "summary": {
    "total_processed": 100,
    "successful": 87,
    "failed": 13,
    "emails_found": 65,
    "phones_found": 42,
    "social_found": 78,
    "avg_processing_time": 9.5
  }
}
```

### 5. Health Check
```
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-26T10:30:00Z",
  "version": "1.0.0",
  "uptime": 3600,
  "active_jobs": 2,
  "system": {
    "cpu_usage": 45.2,
    "memory_usage": 67.8,
    "disk_usage": 23.1
  }
}
```

### 6. API Documentation
```
GET /
GET /docs
GET /redoc
```

## Error Handling

### Standard Error Response
```json
{
  "success": false,
  "error": {
    "code": "INVALID_URL",
    "message": "The provided URL is not valid",
    "details": "URL must start with http:// or https://"
  },
  "timestamp": "2025-01-26T10:30:00Z"
}
```

### Error Codes
- `INVALID_URL`: URL format is invalid
- `URL_UNREACHABLE`: Cannot connect to URL
- `TIMEOUT`: Request timed out
- `RATE_LIMITED`: Too many requests
- `JOB_NOT_FOUND`: Job ID doesn't exist
- `INTERNAL_ERROR`: Server error

## Configuration Options

### Scraping Options
```python
class ScrapingOptions(BaseModel):
    max_pages: int = 5          # Max pages to check per URL
    timeout: int = 30           # Timeout per page in seconds
    max_concurrent: int = 10    # Max concurrent requests
    batch_size: int = 50        # URLs per batch
    include_social: bool = True # Include social media extraction
    include_phone: bool = True  # Include phone extraction
    retry_failed: bool = True   # Retry failed URLs once
```

### Rate Limiting
- Single URL: 60 requests/minute per IP
- Batch processing: 10 jobs/hour per IP
- Max URLs per batch: 1000

## Deployment Configuration

### Environment Variables
```bash
# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# Scraping Configuration
DEFAULT_BATCH_SIZE=50
DEFAULT_MAX_CONCURRENT=10
DEFAULT_TIMEOUT=30

# Storage Configuration
DATABASE_URL=sqlite:///./scraper.db
REDIS_URL=redis://localhost:6379  # Optional

# Security
API_KEY_REQUIRED=false
ALLOWED_ORIGINS=*

# Monitoring
LOG_LEVEL=INFO
ENABLE_METRICS=true
```

### Docker Configuration
- Multi-stage build for optimization
- Health checks included
- Volume mounts for data persistence
- Environment-based configuration
- Production-ready logging

## Performance Targets
- Single URL: < 15 seconds average
- Batch processing: 100 URLs in < 10 minutes
- Concurrent jobs: Up to 5 simultaneous
- Memory usage: < 1GB per worker
- CPU usage: < 80% under load
