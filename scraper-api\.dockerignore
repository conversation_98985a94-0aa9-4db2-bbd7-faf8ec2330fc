# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation
README.md
docs/
*.md

# Tests
tests/
test_*.py
*_test.py

# Logs and data (will be mounted as volumes)
logs/
data/
*.log
*.db
*.sqlite
*.sqlite3

# Development files
.env.example
requirements-dev.txt

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Coverage and testing
.coverage
.pytest_cache/
htmlcov/

# Monitoring configs (will be mounted separately)
monitoring/
nginx/

# Scripts
scripts/
