{"tests/test_api_endpoints.py::TestHealthEndpoints::test_health_check": true, "tests/test_api_endpoints.py::TestHealthEndpoints::test_readiness_check": true, "tests/test_api_endpoints.py::TestHealthEndpoints::test_liveness_check": true, "tests/test_api_endpoints.py::TestRootEndpoint::test_root_endpoint": true, "tests/test_api_endpoints.py::TestScraperEndpoints::test_single_url_valid_request": true, "tests/test_api_endpoints.py::TestScraperEndpoints::test_single_url_minimal_request": true, "tests/test_api_endpoints.py::TestScraperEndpoints::test_single_url_invalid_url": true, "tests/test_api_endpoints.py::TestScraperEndpoints::test_single_url_missing_url": true, "tests/test_api_endpoints.py::TestScraperEndpoints::test_single_url_invalid_options": true, "tests/test_api_endpoints.py::TestScraperEndpoints::test_batch_urls_valid_request": true, "tests/test_api_endpoints.py::TestScraperEndpoints::test_batch_urls_minimal_request": true, "tests/test_api_endpoints.py::TestScraperEndpoints::test_batch_urls_empty_list": true, "tests/test_api_endpoints.py::TestScraperEndpoints::test_batch_urls_too_many_urls": true, "tests/test_api_endpoints.py::TestScraperEndpoints::test_batch_urls_invalid_urls": true, "tests/test_api_endpoints.py::TestScraperEndpoints::test_scraper_stats": true, "tests/test_api_endpoints.py::TestErrorHandling::test_404_endpoint": true, "tests/test_api_endpoints.py::TestErrorHandling::test_405_method_not_allowed": true, "tests/test_api_endpoints.py::TestErrorHandling::test_invalid_json": true, "tests/test_api_endpoints.py::TestCORS::test_cors_headers": true, "tests/test_api_endpoints.py::TestCORS::test_preflight_request": true, "tests/test_core_functionality.py::TestContactExtractor::test_phone_validation": true, "tests/test_core_functionality.py::TestContactExtractor::test_extract_from_html": true, "tests/test_core_functionality.py::TestErrorHandling::test_network_error_handling": true, "tests/test_models.py::TestRequestModels::test_single_url_request_valid": true, "tests/test_models.py::TestRequestModels::test_single_url_request_minimal": true, "tests/test_models.py::TestRequestModels::test_batch_url_request_valid": true, "tests/test_models.py::TestResponseModels::test_error_response": true, "tests/test_models.py::TestResponseModels::test_health_response_with_system_info": true, "tests/test_models.py::TestModelSerialization::test_single_url_request_json": true}