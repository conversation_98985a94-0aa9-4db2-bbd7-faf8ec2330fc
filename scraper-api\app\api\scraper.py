"""
Scraper API endpoints.
"""

from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse

from ..config import get_settings
from ..core.scraper import WebScraperEngine
from ..models.requests import SingleUrlRequest, BatchUrlRequest
from ..models.responses import SingleUrlResponse, BatchUrlResponse, ErrorResponse
from ..utils.logging import get_logger, log_scraping_result, log_batch_result

router = APIRouter()
logger = get_logger(__name__)

# Global scraper engine instance
scraper_engine = None


def get_scraper_engine() -> WebScraperEngine:
    """Get or create the scraper engine instance."""
    global scraper_engine
    if scraper_engine is None:
        settings = get_settings()
        scraper_engine = WebScraperEngine(
            batch_size=settings.default_batch_size,
            max_concurrent=settings.default_max_concurrent,
            default_timeout=settings.default_timeout,
            default_max_pages=settings.default_max_pages
        )
    return scraper_engine


@router.post(
    "/single",
    response_model=SingleUrlResponse,
    summary="Scrape Single URL",
    description="Extract contact information from a single URL"
)
async def scrape_single_url(request: SingleUrlRequest) -> SingleUrlResponse:
    """
    Scrape a single URL for contact information.
    
    This endpoint processes a single URL and returns extracted contact information
    including emails, phone numbers, and social media links.
    
    - **url**: The URL to scrape (must be a valid HTTP/HTTPS URL)
    - **options**: Optional scraping configuration
    
    Returns detailed contact information with confidence scores and metadata.
    """
    logger.info("Single URL scraping requested", url=str(request.url))
    
    try:
        engine = get_scraper_engine()
        
        # Extract options
        options = request.options or {}
        
        # Perform scraping
        result = await engine.scrape_single_url(
            url=str(request.url),
            max_pages=options.max_pages,
            timeout=options.timeout,
            include_social=options.include_social,
            include_phone=options.include_phone
        )
        
        # Log result
        log_scraping_result(
            logger,
            url=str(request.url),
            success=result.get("success", False),
            processing_time=result.get("processing_time", 0)
        )
        
        # Convert to response model
        if result.get("success"):
            return SingleUrlResponse(
                success=True,
                url=result["url"],
                timestamp=datetime.fromisoformat(result["timestamp"].replace('Z', '+00:00')),
                processing_time=result["processing_time"],
                data=result.get("data"),
                metadata=result.get("metadata")
            )
        else:
            # Return error response
            error_info = result.get("error", {})
            if isinstance(error_info, str):
                error_info = {"code": "EXTRACTION_ERROR", "message": error_info}
            
            return SingleUrlResponse(
                success=False,
                url=result.get("url", str(request.url)),
                timestamp=datetime.fromisoformat(result.get("timestamp", datetime.now().isoformat()).replace('Z', '+00:00')),
                processing_time=result.get("processing_time", 0),
                error=error_info
            )
    
    except Exception as e:
        logger.error("Error in single URL scraping", error=str(e), url=str(request.url), exc_info=True)
        
        return SingleUrlResponse(
            success=False,
            url=str(request.url),
            timestamp=datetime.now(),
            processing_time=0,
            error={
                "code": "INTERNAL_ERROR",
                "message": "An internal error occurred during scraping",
                "details": str(e)
            }
        )


@router.post(
    "/batch",
    response_model=BatchUrlResponse,
    summary="Scrape Multiple URLs",
    description="Extract contact information from multiple URLs in batch"
)
async def scrape_batch_urls(request: BatchUrlRequest) -> BatchUrlResponse:
    """
    Scrape multiple URLs for contact information.
    
    This endpoint processes multiple URLs in batch and returns extracted contact
    information for each URL along with a summary of results.
    
    - **urls**: List of URLs to scrape (1-1000 URLs)
    - **options**: Optional scraping configuration
    - **batch_options**: Batch processing configuration
    - **callback_url**: Optional webhook URL for completion notification
    
    Returns individual results for each URL plus batch summary statistics.
    """
    logger.info("Batch URL scraping requested", url_count=len(request.urls))
    
    try:
        engine = get_scraper_engine()
        
        # Convert URLs to strings
        urls = [str(url) for url in request.urls]
        
        # Extract options
        options = request.options or {}
        batch_options = request.batch_options or {}
        
        # Update engine settings for this batch if specified
        if hasattr(batch_options, 'batch_size') and batch_options.batch_size:
            engine.batch_size = batch_options.batch_size
        if hasattr(batch_options, 'max_concurrent') and batch_options.max_concurrent:
            engine.max_concurrent = batch_options.max_concurrent
        
        # Perform batch scraping
        result = await engine.scrape_batch_urls(
            urls=urls,
            max_pages=options.max_pages,
            timeout=options.timeout,
            include_social=options.include_social,
            include_phone=options.include_phone
        )
        
        # Log batch result
        if result.get("success"):
            summary = result.get("summary", {})
            log_batch_result(
                logger,
                total_urls=summary.get("total_urls", 0),
                successful=summary.get("successful", 0),
                failed=summary.get("failed", 0),
                processing_time=result.get("total_processing_time", 0)
            )
        
        # Convert individual results to response models
        if result.get("success") and result.get("results"):
            converted_results = []
            for res in result["results"]:
                if res.get("success"):
                    converted_results.append(SingleUrlResponse(
                        success=True,
                        url=res["url"],
                        timestamp=datetime.fromisoformat(res["timestamp"].replace('Z', '+00:00')),
                        processing_time=res.get("processing_time", 0),
                        data=res.get("data"),
                        metadata=res.get("metadata")
                    ))
                else:
                    error_info = res.get("error", {})
                    if isinstance(error_info, str):
                        error_info = {"code": "EXTRACTION_ERROR", "message": error_info}
                    
                    converted_results.append(SingleUrlResponse(
                        success=False,
                        url=res.get("url", ""),
                        timestamp=datetime.fromisoformat(res.get("timestamp", datetime.now().isoformat()).replace('Z', '+00:00')),
                        processing_time=res.get("processing_time", 0),
                        error=error_info
                    ))
            
            return BatchUrlResponse(
                success=True,
                total_processing_time=result["total_processing_time"],
                timestamp=datetime.fromisoformat(result["timestamp"].replace('Z', '+00:00')),
                results=converted_results,
                summary=result.get("summary")
            )
        else:
            # Return error response
            error_info = result.get("error", {})
            if isinstance(error_info, str):
                error_info = {"code": "BATCH_ERROR", "message": error_info}
            
            return BatchUrlResponse(
                success=False,
                total_processing_time=result.get("total_processing_time", 0),
                timestamp=datetime.now(),
                error=error_info
            )
    
    except Exception as e:
        logger.error("Error in batch URL scraping", error=str(e), url_count=len(request.urls), exc_info=True)
        
        return BatchUrlResponse(
            success=False,
            total_processing_time=0,
            timestamp=datetime.now(),
            error={
                "code": "INTERNAL_ERROR",
                "message": "An internal error occurred during batch scraping",
                "details": str(e)
            }
        )


@router.get(
    "/stats",
    summary="Get Scraper Statistics",
    description="Get current scraper engine statistics and configuration"
)
async def get_scraper_stats() -> Dict[str, Any]:
    """Get scraper engine statistics and configuration."""
    try:
        engine = get_scraper_engine()
        stats = engine.get_stats()
        
        return {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "stats": stats
        }
    
    except Exception as e:
        logger.error("Error getting scraper stats", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to get scraper statistics")
