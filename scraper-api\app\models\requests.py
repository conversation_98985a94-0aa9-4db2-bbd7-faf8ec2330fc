"""
Request models for the Web Scraper API.
"""

from typing import List, Optional
from pydantic import BaseModel, Field, validator, HttpUrl

from ..utils.validation import (
    validate_batch_size, validate_timeout, validate_max_pages, 
    validate_max_concurrent
)


class ScrapingOptions(BaseModel):
    """Options for scraping configuration."""
    
    max_pages: int = Field(
        default=5,
        ge=1,
        le=20,
        description="Maximum pages to check per URL"
    )
    
    timeout: int = Field(
        default=30,
        ge=5,
        le=300,
        description="Timeout per page in seconds"
    )
    
    include_social: bool = Field(
        default=True,
        description="Whether to extract social media links"
    )
    
    include_phone: bool = Field(
        default=True,
        description="Whether to extract phone numbers"
    )
    
    @validator('max_pages')
    def validate_max_pages_value(cls, v):
        if not validate_max_pages(v):
            raise ValueError('max_pages must be between 1 and 20')
        return v
    
    @validator('timeout')
    def validate_timeout_value(cls, v):
        if not validate_timeout(v):
            raise ValueError('timeout must be between 5 and 300 seconds')
        return v


class SingleUrlRequest(BaseModel):
    """Request model for single URL scraping."""
    
    url: HttpUrl = Field(
        description="URL to scrape for contact information"
    )
    
    options: Optional[ScrapingOptions] = Field(
        default_factory=ScrapingOptions,
        description="Scraping options and configuration"
    )
    
    class Config:
        schema_extra = {
            "example": {
                "url": "https://example.com",
                "options": {
                    "max_pages": 5,
                    "timeout": 30,
                    "include_social": True,
                    "include_phone": True
                }
            }
        }


class BatchUrlRequest(BaseModel):
    """Request model for batch URL scraping."""
    
    urls: List[HttpUrl] = Field(
        min_items=1,
        max_items=1000,
        description="List of URLs to scrape for contact information"
    )
    
    options: Optional[ScrapingOptions] = Field(
        default_factory=ScrapingOptions,
        description="Scraping options and configuration"
    )
    
    batch_options: Optional['BatchOptions'] = Field(
        default_factory=lambda: BatchOptions(),
        description="Batch processing specific options"
    )
    
    callback_url: Optional[HttpUrl] = Field(
        default=None,
        description="Optional webhook URL to notify when batch is complete"
    )
    
    @validator('urls')
    def validate_urls_list(cls, v):
        if len(v) > 1000:
            raise ValueError('Maximum 1000 URLs allowed per batch')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "urls": [
                    "https://example1.com",
                    "https://example2.com",
                    "https://example3.com"
                ],
                "options": {
                    "max_pages": 5,
                    "timeout": 30,
                    "include_social": True,
                    "include_phone": True
                },
                "batch_options": {
                    "batch_size": 50,
                    "max_concurrent": 10
                },
                "callback_url": "https://your-app.com/webhook"
            }
        }


class BatchOptions(BaseModel):
    """Batch processing specific options."""
    
    batch_size: int = Field(
        default=50,
        ge=1,
        le=200,
        description="Number of URLs to process in each batch"
    )
    
    max_concurrent: int = Field(
        default=10,
        ge=1,
        le=50,
        description="Maximum concurrent requests per batch"
    )
    
    @validator('batch_size')
    def validate_batch_size_value(cls, v):
        if not validate_batch_size(v, max_allowed=200):
            raise ValueError('batch_size must be between 1 and 200')
        return v
    
    @validator('max_concurrent')
    def validate_max_concurrent_value(cls, v):
        if not validate_max_concurrent(v):
            raise ValueError('max_concurrent must be between 1 and 50')
        return v


# Update forward reference
BatchUrlRequest.model_rebuild()
