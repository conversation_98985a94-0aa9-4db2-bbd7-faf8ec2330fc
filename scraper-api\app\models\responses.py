"""
Response models for the Web Scraper API.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class EmailData(BaseModel):
    """Email contact data."""
    
    email: str = Field(description="Email address")
    confidence: float = Field(
        ge=0.0, 
        le=1.0, 
        description="Confidence score for the email (0.0 to 1.0)"
    )


class PhoneData(BaseModel):
    """Phone contact data."""
    
    phone: str = Field(description="Phone number")
    confidence: float = Field(
        ge=0.0, 
        le=1.0, 
        description="Confidence score for the phone number (0.0 to 1.0)"
    )


class SocialMediaData(BaseModel):
    """Social media contact data."""
    
    platform: str = Field(description="Social media platform name")
    url: str = Field(description="Social media profile URL")
    handle: Optional[str] = Field(
        default=None, 
        description="Social media handle/username"
    )
    confidence: float = Field(
        ge=0.0, 
        le=1.0, 
        description="Confidence score for the social media link (0.0 to 1.0)"
    )


class ContactData(BaseModel):
    """Contact information extracted from a URL."""
    
    email: Optional[EmailData] = Field(
        default=None,
        description="Email contact information"
    )
    
    phone: Optional[PhoneData] = Field(
        default=None,
        description="Phone contact information"
    )
    
    social_media: Optional[SocialMediaData] = Field(
        default=None,
        description="Social media contact information"
    )


class ScrapingMetadata(BaseModel):
    """Metadata about the scraping process."""
    
    pages_available: int = Field(
        description="Total number of pages available to check"
    )
    
    pages_checked: int = Field(
        description="Number of pages actually checked"
    )
    
    pages_with_content: int = Field(
        description="Number of pages that had content"
    )
    
    efficiency: str = Field(
        description="Efficiency ratio (pages_checked/pages_available)"
    )
    
    website_type: str = Field(
        description="Detected website type (business, government, medical, education)"
    )


class ErrorData(BaseModel):
    """Error information."""
    
    code: str = Field(description="Error code")
    message: str = Field(description="Human-readable error message")
    details: Optional[str] = Field(
        default=None,
        description="Additional error details"
    )


class SingleUrlResponse(BaseModel):
    """Response model for single URL scraping."""
    
    success: bool = Field(description="Whether the scraping was successful")
    url: str = Field(description="The URL that was scraped")
    timestamp: datetime = Field(description="When the scraping was completed")
    processing_time: float = Field(description="Processing time in seconds")
    
    data: Optional[ContactData] = Field(
        default=None,
        description="Extracted contact data (only present if successful)"
    )
    
    metadata: Optional[ScrapingMetadata] = Field(
        default=None,
        description="Scraping metadata (only present if successful)"
    )
    
    error: Optional[ErrorData] = Field(
        default=None,
        description="Error information (only present if failed)"
    )
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "url": "https://example.com",
                "timestamp": "2025-01-26T10:30:00Z",
                "processing_time": 12.5,
                "data": {
                    "email": {
                        "email": "<EMAIL>",
                        "confidence": 0.95
                    },
                    "phone": {
                        "phone": "******-123-4567",
                        "confidence": 0.90
                    },
                    "social_media": {
                        "platform": "instagram",
                        "url": "https://instagram.com/example",
                        "handle": "@example",
                        "confidence": 0.85
                    }
                },
                "metadata": {
                    "pages_checked": 3,
                    "pages_available": 5,
                    "pages_with_content": 2,
                    "efficiency": "3/5",
                    "website_type": "business"
                }
            }
        }


class BatchSummary(BaseModel):
    """Summary of batch processing results."""
    
    total_urls: int = Field(description="Total number of URLs in the batch")
    valid_urls: int = Field(description="Number of valid URLs")
    invalid_urls: int = Field(description="Number of invalid URLs")
    successful: int = Field(description="Number of successful extractions")
    failed: int = Field(description="Number of failed extractions")
    emails_found: int = Field(description="Number of URLs where emails were found")
    phones_found: int = Field(description="Number of URLs where phones were found")
    socials_found: int = Field(description="Number of URLs where social media was found")
    avg_processing_time: float = Field(description="Average processing time per URL")


class BatchUrlResponse(BaseModel):
    """Response model for batch URL scraping."""
    
    success: bool = Field(description="Whether the batch processing was successful")
    total_processing_time: float = Field(description="Total processing time in seconds")
    timestamp: datetime = Field(description="When the batch processing was completed")
    
    results: Optional[List[SingleUrlResponse]] = Field(
        default=None,
        description="Individual results for each URL (only present if successful)"
    )
    
    summary: Optional[BatchSummary] = Field(
        default=None,
        description="Summary of batch results (only present if successful)"
    )
    
    error: Optional[ErrorData] = Field(
        default=None,
        description="Error information (only present if failed)"
    )


class ErrorResponse(BaseModel):
    """Standard error response."""
    
    success: bool = Field(default=False, description="Always false for error responses")
    error: ErrorData = Field(description="Error information")
    timestamp: datetime = Field(description="When the error occurred")
    
    class Config:
        schema_extra = {
            "example": {
                "success": False,
                "error": {
                    "code": "INVALID_URL",
                    "message": "The provided URL is not valid",
                    "details": "URL must start with http:// or https://"
                },
                "timestamp": "2025-01-26T10:30:00Z"
            }
        }


class SystemInfo(BaseModel):
    """System information for health checks."""
    
    cpu_usage: Optional[float] = Field(
        default=None,
        description="CPU usage percentage"
    )
    
    memory_usage: Optional[float] = Field(
        default=None,
        description="Memory usage percentage"
    )
    
    disk_usage: Optional[float] = Field(
        default=None,
        description="Disk usage percentage"
    )


class HealthResponse(BaseModel):
    """Health check response."""
    
    status: str = Field(description="Health status (healthy, unhealthy, degraded)")
    timestamp: datetime = Field(description="Health check timestamp")
    version: str = Field(description="API version")
    uptime: int = Field(description="Uptime in seconds")
    active_jobs: int = Field(description="Number of active jobs")
    
    system: Optional[SystemInfo] = Field(
        default=None,
        description="System information"
    )
    
    class Config:
        schema_extra = {
            "example": {
                "status": "healthy",
                "timestamp": "2025-01-26T10:30:00Z",
                "version": "1.0.0",
                "uptime": 3600,
                "active_jobs": 2,
                "system": {
                    "cpu_usage": 45.2,
                    "memory_usage": 67.8,
                    "disk_usage": 23.1
                }
            }
        }
