"""
Main web scraper engine that orchestrates contact extraction.
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, List, Optional, Union

from .extractor import ContactExtractor
from ..utils.validation import validate_url, validate_urls


class WebScraperEngine:
    """
    Main web scraper engine that orchestrates contact extraction.
    Provides high-level interface for single URL and batch processing.
    """
    
    def __init__(self, 
                 batch_size: int = 50,
                 max_concurrent: int = 10,
                 default_timeout: int = 30,
                 default_max_pages: int = 5):
        """
        Initialize the web scraper engine.
        
        Args:
            batch_size: URLs per batch for processing
            max_concurrent: Maximum concurrent requests
            default_timeout: Default timeout per page in seconds
            default_max_pages: Default maximum pages to check per URL
        """
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        self.default_timeout = default_timeout
        self.default_max_pages = default_max_pages
        
        # Initialize the contact extractor
        self.extractor = ContactExtractor(
            batch_size=batch_size,
            max_concurrent=max_concurrent
        )
    
    async def scrape_single_url(self, 
                               url: str,
                               max_pages: Optional[int] = None,
                               timeout: Optional[int] = None,
                               include_social: bool = True,
                               include_phone: bool = True) -> Dict:
        """
        Scrape a single URL for contact information.
        
        Args:
            url: URL to scrape
            max_pages: Maximum pages to check (uses default if None)
            timeout: Timeout per page in seconds (uses default if None)
            include_social: Whether to extract social media links
            include_phone: Whether to extract phone numbers
            
        Returns:
            Dictionary with extraction results and metadata
        """
        # Validate URL
        if not validate_url(url):
            return {
                "success": False,
                "error": {
                    "code": "INVALID_URL",
                    "message": "The provided URL is not valid",
                    "details": "URL must start with http:// or https://"
                },
                "timestamp": datetime.now().isoformat()
            }
        
        # Use defaults if not provided
        max_pages = max_pages or self.default_max_pages
        timeout = timeout or self.default_timeout
        
        start_time = time.time()
        
        try:
            # Extract contacts
            results = await self.extractor.extract_contacts(
                urls=[url],
                max_pages=max_pages,
                timeout=timeout,
                include_social=include_social,
                include_phone=include_phone
            )
            
            if not results:
                return {
                    "success": False,
                    "error": {
                        "code": "NO_RESULTS",
                        "message": "No results returned from extraction",
                        "details": "The extraction process completed but returned no results"
                    },
                    "timestamp": datetime.now().isoformat()
                }
            
            result = results[0]
            processing_time = time.time() - start_time
            
            # Add processing time to the result
            if result.get("success"):
                result["processing_time"] = round(processing_time, 2)
                return result
            else:
                # Handle extraction errors
                return {
                    "success": False,
                    "url": url,
                    "processing_time": round(processing_time, 2),
                    "error": {
                        "code": "EXTRACTION_ERROR",
                        "message": result.get("error", "Unknown extraction error"),
                        "details": "An error occurred during the extraction process"
                    },
                    "timestamp": result.get("timestamp", datetime.now().isoformat())
                }
        
        except asyncio.TimeoutError:
            return {
                "success": False,
                "url": url,
                "processing_time": round(time.time() - start_time, 2),
                "error": {
                    "code": "TIMEOUT",
                    "message": "Request timed out",
                    "details": f"The request exceeded the timeout limit of {timeout} seconds"
                },
                "timestamp": datetime.now().isoformat()
            }
        
        except Exception as e:
            return {
                "success": False,
                "url": url,
                "processing_time": round(time.time() - start_time, 2),
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": "An internal error occurred",
                    "details": str(e)
                },
                "timestamp": datetime.now().isoformat()
            }
    
    async def scrape_batch_urls(self,
                               urls: List[str],
                               max_pages: Optional[int] = None,
                               timeout: Optional[int] = None,
                               include_social: bool = True,
                               include_phone: bool = True,
                               progress_callback: Optional[callable] = None) -> Dict:
        """
        Scrape multiple URLs for contact information.
        
        Args:
            urls: List of URLs to scrape
            max_pages: Maximum pages to check per URL
            timeout: Timeout per page in seconds
            include_social: Whether to extract social media links
            include_phone: Whether to extract phone numbers
            progress_callback: Optional callback function for progress updates
            
        Returns:
            Dictionary with batch results and summary
        """
        # Validate URLs
        valid_urls = []
        invalid_urls = []
        
        for url in urls:
            if validate_url(url):
                valid_urls.append(url)
            else:
                invalid_urls.append(url)
        
        if not valid_urls:
            return {
                "success": False,
                "error": {
                    "code": "NO_VALID_URLS",
                    "message": "No valid URLs provided",
                    "details": f"All {len(urls)} URLs were invalid"
                },
                "timestamp": datetime.now().isoformat()
            }
        
        # Use defaults if not provided
        max_pages = max_pages or self.default_max_pages
        timeout = timeout or self.default_timeout
        
        start_time = time.time()
        
        try:
            # Extract contacts from all valid URLs
            results = await self.extractor.extract_contacts(
                urls=valid_urls,
                max_pages=max_pages,
                timeout=timeout,
                include_social=include_social,
                include_phone=include_phone
            )
            
            # Add invalid URLs to results
            for invalid_url in invalid_urls:
                results.append({
                    "url": invalid_url,
                    "success": False,
                    "error": "Invalid URL format",
                    "timestamp": datetime.now().isoformat()
                })
            
            processing_time = time.time() - start_time
            
            # Generate summary
            successful_results = [r for r in results if r.get("success")]
            failed_results = [r for r in results if not r.get("success")]
            
            emails_found = len([r for r in successful_results if r.get("data", {}).get("email")])
            phones_found = len([r for r in successful_results if r.get("data", {}).get("phone")])
            socials_found = len([r for r in successful_results if r.get("data", {}).get("social_media")])
            
            avg_processing_time = 0
            if successful_results:
                total_time = sum(r.get("processing_time", 0) for r in successful_results if "processing_time" in r)
                avg_processing_time = total_time / len(successful_results) if total_time > 0 else processing_time / len(successful_results)
            
            return {
                "success": True,
                "total_processing_time": round(processing_time, 2),
                "timestamp": datetime.now().isoformat(),
                "results": results,
                "summary": {
                    "total_urls": len(urls),
                    "valid_urls": len(valid_urls),
                    "invalid_urls": len(invalid_urls),
                    "successful": len(successful_results),
                    "failed": len(failed_results),
                    "emails_found": emails_found,
                    "phones_found": phones_found,
                    "socials_found": socials_found,
                    "avg_processing_time": round(avg_processing_time, 2)
                }
            }
        
        except Exception as e:
            return {
                "success": False,
                "total_processing_time": round(time.time() - start_time, 2),
                "error": {
                    "code": "BATCH_ERROR",
                    "message": "An error occurred during batch processing",
                    "details": str(e)
                },
                "timestamp": datetime.now().isoformat()
            }
    
    def get_stats(self) -> Dict:
        """Get scraper engine statistics."""
        return {
            "batch_size": self.batch_size,
            "max_concurrent": self.max_concurrent,
            "default_timeout": self.default_timeout,
            "default_max_pages": self.default_max_pages,
            "engine_status": "ready"
        }
