"""
Configuration settings for the Web Scraper API.
"""

import os
from typing import List, Optional
from pydantic import BaseModel, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # API Configuration
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    api_workers: int = 4
    api_title: str = "Web Scraper API"
    api_version: str = "1.0.0"
    api_description: str = "High-performance web scraper for contact information extraction"
    
    # Security
    secret_key: str = "change-me-in-production"
    api_key_required: bool = False
    allowed_origins: str = "*"
    cors_enabled: bool = True
    
    # Scraping Configuration
    default_batch_size: int = 50
    default_max_concurrent: int = 10
    default_timeout: int = 30
    default_max_pages: int = 5
    max_urls_per_batch: int = 1000
    
    # Rate Limiting
    rate_limit_single: int = 60  # requests per minute
    rate_limit_batch: int = 10   # jobs per hour
    rate_limit_enabled: bool = True
    
    # Database Configuration
    database_url: str = "sqlite:///./scraper.db"
    database_echo: bool = False
    database_pool_size: int = 10
    database_max_overflow: int = 20
    
    # Redis Configuration (Optional)
    redis_url: Optional[str] = None
    redis_enabled: bool = False
    
    # Job Configuration
    max_concurrent_jobs: int = 5
    job_timeout: int = 3600  # 1 hour
    job_cleanup_interval: int = 86400  # 24 hours
    job_retention_days: int = 7
    
    # Logging Configuration
    log_level: str = "INFO"
    log_format: str = "json"
    log_file: Optional[str] = None
    log_rotation: str = "daily"
    log_retention: int = 30
    
    # Monitoring
    enable_metrics: bool = True
    metrics_port: int = 9090
    health_check_interval: int = 30
    
    # Performance Tuning
    worker_connections: int = 1000
    worker_timeout: int = 120
    keep_alive: int = 2
    
    # Development
    debug: bool = False
    reload: bool = False
    access_log: bool = True
    
    # External Services
    user_agent: str = "ScraperAPI/1.0 (+https://your-domain.com/bot)"
    respect_robots_txt: bool = True
    crawl_delay: float = 1.0
    
    # Storage
    results_storage_path: str = "./data/results"
    temp_storage_path: str = "./data/temp"
    max_result_size_mb: int = 100
    
    @field_validator('allowed_origins', mode='before')
    @classmethod
    def parse_cors_origins(cls, v):
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            if v == "*":
                return "*"
            return v.strip()
        return v

    @field_validator('log_level')
    @classmethod
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of: {valid_levels}')
        return v.upper()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        # Don't fail if .env file doesn't exist
        env_ignore_empty = True


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings
