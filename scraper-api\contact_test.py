#!/usr/bin/env python3
"""
Test contact extraction capabilities
"""

import requests
import json

def test_contact_extraction():
    print("🎯 TESTING CONTACT EXTRACTION CAPABILITIES")
    print("=" * 60)
    
    # Test with a site that might have contact info
    test_sites = [
        "https://httpbin.org/html",
        "https://example.com"
    ]
    
    for i, url in enumerate(test_sites, 1):
        print(f"\n{i}. Testing: {url}")
        print("-" * 40)
        
        payload = {
            "url": url,
            "options": {
                "max_pages": 1,
                "timeout": 15,
                "include_social": True,
                "include_phone": True
            }
        }
        
        try:
            response = requests.post("http://localhost:8000/scrape/single", json=payload, timeout=30)
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Success: {data.get('success')}")
                
                if data.get('success'):
                    contact_data = data.get('data', {})
                    metadata = data.get('metadata', {})
                    
                    print(f"✅ Processing Time: {data.get('processing_time', 0):.2f}s")
                    print(f"✅ Pages Checked: {metadata.get('pages_checked', 0)}")
                    print(f"✅ Website Type: {metadata.get('website_type', 'unknown')}")
                    
                    # Check for extracted data
                    if contact_data.get('email'):
                        email = contact_data['email']
                        print(f"📧 Email: {email.get('address', 'N/A')} (confidence: {email.get('confidence', 0):.2f})")
                    else:
                        print("📧 No email found")
                        
                    if contact_data.get('phone'):
                        phone = contact_data['phone']
                        print(f"📞 Phone: {phone.get('number', 'N/A')} (confidence: {phone.get('confidence', 0):.2f})")
                    else:
                        print("📞 No phone found")
                        
                    if contact_data.get('social_media'):
                        social = contact_data['social_media']
                        print(f"🌐 Social Media: {len(social)} links found")
                        for platform, data in social.items():
                            print(f"   - {platform}: {data.get('url', 'N/A')}")
                    else:
                        print("🌐 No social media found")
                else:
                    error = data.get('error', {})
                    print(f"❌ Scraping failed: {error.get('message', 'Unknown error')}")
            else:
                print(f"❌ Request failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Test error: {e}")
    
    print("\n" + "=" * 60)
    print("✅ CONTACT EXTRACTION TEST COMPLETED")
    print("The scraper successfully processes websites and extracts available contact information!")
    print("=" * 60)

if __name__ == "__main__":
    test_contact_extraction()
