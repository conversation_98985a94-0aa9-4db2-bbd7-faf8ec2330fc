version: '3.8'

services:
  # Main API service
  scraper-api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: scraper-api
    ports:
      - "8000:8000"
    environment:
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - API_WORKERS=4
      - LOG_LEVEL=INFO
      - DATABASE_URL=sqlite:///./data/scraper.db
      - DEFAULT_BATCH_SIZE=50
      - DEFAULT_MAX_CONCURRENT=10
      - DEFAULT_TIMEOUT=30
      - CORS_ENABLED=true
      - ALLOWED_ORIGINS=*
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - scraper-network

  # Optional: Redis for job queue (uncomment if needed)
  # redis:
  #   image: redis:7-alpine
  #   container_name: scraper-redis
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   restart: unless-stopped
  #   networks:
  #     - scraper-network

  # Optional: Nginx reverse proxy (uncomment for production)
  # nginx:
  #   image: nginx:alpine
  #   container_name: scraper-nginx
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   volumes:
  #     - ./nginx.conf:/etc/nginx/nginx.conf:ro
  #     - ./ssl:/etc/nginx/ssl:ro
  #   depends_on:
  #     - scraper-api
  #   restart: unless-stopped
  #   networks:
  #     - scraper-network

networks:
  scraper-network:
    driver: bridge

volumes:
  # redis_data:  # Uncomment if using Redis
  api_data:
  api_logs:
