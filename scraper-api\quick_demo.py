#!/usr/bin/env python3
"""
Quick Demo of Web Scraper API Capabilities
Tests the Docker scraper with a curated set of URLs that demonstrate different extraction scenarios.
"""

import requests
import json
import time
from datetime import datetime

API_BASE_URL = "http://localhost:8000"

# Curated test URLs with different characteristics
TEST_URLS = [
    {
        "url": "https://httpbin.org/html",
        "description": "Simple HTML test page",
        "expected": "No contact info (baseline test)"
    },
    {
        "url": "https://example.com",
        "description": "Basic example website",
        "expected": "No contact info (error handling test)"
    },
    {
        "url": "https://www.iana.org/contact",
        "description": "IANA contact page",
        "expected": "Email addresses and contact forms"
    },
    {
        "url": "https://www.rfc-editor.org/contact/",
        "description": "RFC Editor contact page", 
        "expected": "Email addresses"
    },
    {
        "url": "https://www.w3.org/Consortium/contact",
        "description": "W3C contact information",
        "expected": "Email addresses and phone numbers"
    }
]

# Batch test with mixed URLs
BATCH_TEST_URLS = [
    "https://httpbin.org/html",
    "https://example.com", 
    "https://www.iana.org/contact",
    "https://www.rfc-editor.org/contact/",
    "https://www.w3.org/Consortium/contact",
    "https://httpbin.org/json",
    "https://www.ietf.org/contact/",
    "https://tools.ietf.org/"
]

def check_api_health():
    """Check if API is running."""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def test_single_url(url_data):
    """Test a single URL."""
    print(f"🔍 Testing: {url_data['url']}")
    print(f"   📝 {url_data['description']}")
    print(f"   🎯 Expected: {url_data['expected']}")
    
    try:
        payload = {
            "url": url_data['url'],
            "options": {
                "timeout": 10,
                "max_pages": 1
            }
        }
        
        start_time = time.time()
        response = requests.post(
            f"{API_BASE_URL}/scrape/single",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=20
        )
        processing_time = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            success = data.get('success', False)
            
            if success and data.get('data'):
                contacts = data['data']
                emails = len(contacts.get('emails', []))
                phones = len(contacts.get('phones', []))
                social = len(contacts.get('social_links', []))
                
                print(f"   ✅ SUCCESS - Found: {emails} emails, {phones} phones, {social} social links")
                print(f"   ⏱️  Processing time: {processing_time:.2f}s")
                
                # Show actual extracted data
                if contacts.get('emails'):
                    print(f"   📧 Emails: {', '.join(contacts['emails'][:3])}")
                if contacts.get('phones'):
                    print(f"   📞 Phones: {', '.join(contacts['phones'][:2])}")
                if contacts.get('social_links'):
                    platforms = [link.get('platform', 'unknown') for link in contacts['social_links'][:3]]
                    print(f"   🔗 Social: {', '.join(platforms)}")
            else:
                error_msg = data.get('error', {}).get('message', 'No contacts found')
                print(f"   ⚠️  No contacts extracted: {error_msg}")
                print(f"   ⏱️  Processing time: {processing_time:.2f}s")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Request failed: {str(e)}")
    
    print()

def test_batch_processing():
    """Test batch processing."""
    print("🚀 BATCH PROCESSING TEST")
    print("=" * 50)
    print(f"📦 Testing {len(BATCH_TEST_URLS)} URLs in batch mode")
    print()
    
    try:
        payload = {
            "urls": BATCH_TEST_URLS,
            "batch_options": {
                "batch_size": 4,
                "max_concurrent": 3
            },
            "scraping_options": {
                "timeout": 10,
                "max_pages": 1
            }
        }
        
        start_time = time.time()
        response = requests.post(
            f"{API_BASE_URL}/scrape/batch",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        total_time = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            results = data.get('results', [])
            
            successful = sum(1 for r in results if r.get('success'))
            total_emails = 0
            total_phones = 0
            total_social = 0
            
            print(f"✅ Batch completed in {total_time:.2f}s")
            print(f"📊 Results: {successful}/{len(results)} URLs processed successfully")
            print(f"⚡ Average time per URL: {total_time/len(BATCH_TEST_URLS):.2f}s")
            print()
            
            for i, result in enumerate(results):
                url = result.get('url', 'Unknown')[:50] + "..." if len(result.get('url', '')) > 50 else result.get('url', 'Unknown')
                status = "✅" if result.get('success') else "❌"
                
                if result.get('success') and result.get('data'):
                    data_result = result['data']
                    emails = len(data_result.get('emails', []))
                    phones = len(data_result.get('phones', []))
                    social = len(data_result.get('social_links', []))
                    
                    total_emails += emails
                    total_phones += phones
                    total_social += social
                    
                    print(f"   {status} {url} - {emails}📧 {phones}📞 {social}🔗")
                else:
                    print(f"   {status} {url} - No contacts")
            
            print()
            print(f"🎯 TOTAL EXTRACTED:")
            print(f"   📧 Emails: {total_emails}")
            print(f"   📞 Phones: {total_phones}")
            print(f"   🔗 Social Links: {total_social}")
            
        else:
            print(f"❌ Batch request failed: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Batch processing failed: {str(e)}")
    
    print()

def main():
    """Run the quick demonstration."""
    print("🚀 QUICK WEB SCRAPER API DEMONSTRATION")
    print("=" * 60)
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check API health
    if not check_api_health():
        print("❌ API is not accessible. Make sure the Docker container is running:")
        print("   docker run -d -p 8000:8000 --name scraper-api-test scraper-api")
        return
    
    print("✅ API is healthy and ready!")
    print()
    
    # Test individual URLs
    print("🔍 SINGLE URL TESTING")
    print("=" * 50)
    for url_data in TEST_URLS:
        test_single_url(url_data)
        time.sleep(0.5)  # Brief pause between requests
    
    # Test batch processing
    test_batch_processing()
    
    # Summary
    print("🎉 DEMONSTRATION COMPLETE!")
    print("=" * 60)
    print("✅ Single URL scraping: Tested with various website types")
    print("✅ Batch processing: Demonstrated concurrent processing")
    print("✅ Contact extraction: Emails, phones, and social links")
    print("✅ Error handling: Graceful handling of problematic URLs")
    print("✅ Performance: Fast processing with detailed metrics")
    print()
    print("📖 Full API Documentation: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print()
    print("🎯 The scraper is ready to process hundreds of URLs efficiently!")

if __name__ == "__main__":
    main()
