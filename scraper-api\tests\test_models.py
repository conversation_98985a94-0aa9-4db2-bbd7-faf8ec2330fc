"""
Test Pydantic models.
"""

import pytest
from datetime import datetime
from pydantic import ValidationError

from app.models.requests import SingleUrlRequest, BatchUrlRequest, ScrapingOptions
from app.models.responses import (
    SingleUrlResponse, BatchUrlResponse, ContactData, EmailData, 
    PhoneData, SocialMediaData, ErrorResponse, HealthResponse
)


class TestRequestModels:
    """Test request models."""
    
    def test_scraping_options_valid(self):
        """Test valid ScrapingOptions."""
        options = ScrapingOptions(
            max_pages=5,
            timeout=30,
            include_social=True,
            include_phone=True
        )
        
        assert options.max_pages == 5
        assert options.timeout == 30
        assert options.include_social is True
        assert options.include_phone is True
    
    def test_scraping_options_defaults(self):
        """Test ScrapingOptions defaults."""
        options = ScrapingOptions()
        
        assert options.max_pages == 5
        assert options.timeout == 30
        assert options.include_social is True
        assert options.include_phone is True
    
    def test_scraping_options_invalid_max_pages(self):
        """Test invalid max_pages in ScrapingOptions."""
        with pytest.raises(ValidationError):
            ScrapingOptions(max_pages=0)
        
        with pytest.raises(ValidationError):
            ScrapingOptions(max_pages=25)
    
    def test_scraping_options_invalid_timeout(self):
        """Test invalid timeout in ScrapingOptions."""
        with pytest.raises(ValidationError):
            ScrapingOptions(timeout=1)
        
        with pytest.raises(ValidationError):
            ScrapingOptions(timeout=500)
    
    def test_single_url_request_valid(self):
        """Test valid SingleUrlRequest."""
        request = SingleUrlRequest(
            url="https://example.com",
            options=ScrapingOptions(max_pages=3)
        )
        
        assert str(request.url) == "https://example.com/"
        assert request.options.max_pages == 3

    def test_single_url_request_minimal(self):
        """Test minimal SingleUrlRequest."""
        request = SingleUrlRequest(url="https://example.com")

        assert str(request.url) == "https://example.com/"
        assert request.options is not None
        assert request.options.max_pages == 5  # Default
    
    def test_single_url_request_invalid_url(self):
        """Test invalid URL in SingleUrlRequest."""
        with pytest.raises(ValidationError):
            SingleUrlRequest(url="not-a-url")
        
        with pytest.raises(ValidationError):
            SingleUrlRequest(url="ftp://example.com")
    
    def test_batch_url_request_valid(self):
        """Test valid BatchUrlRequest."""
        request = BatchUrlRequest(
            urls=["https://example1.com", "https://example2.com"],
            options=ScrapingOptions(max_pages=2)
        )
        
        assert len(request.urls) == 2
        assert str(request.urls[0]) == "https://example1.com/"
        assert request.options.max_pages == 2
    
    def test_batch_url_request_empty_urls(self):
        """Test empty URLs in BatchUrlRequest."""
        with pytest.raises(ValidationError):
            BatchUrlRequest(urls=[])
    
    def test_batch_url_request_too_many_urls(self):
        """Test too many URLs in BatchUrlRequest."""
        urls = ["https://example.com"] * 1001
        with pytest.raises(ValidationError):
            BatchUrlRequest(urls=urls)
    
    def test_batch_url_request_invalid_urls(self):
        """Test invalid URLs in BatchUrlRequest."""
        with pytest.raises(ValidationError):
            BatchUrlRequest(urls=["https://example.com", "not-a-url"])


class TestResponseModels:
    """Test response models."""
    
    def test_email_data_valid(self):
        """Test valid EmailData."""
        email = EmailData(email="<EMAIL>", confidence=0.95)
        
        assert email.email == "<EMAIL>"
        assert email.confidence == 0.95
    
    def test_email_data_invalid_confidence(self):
        """Test invalid confidence in EmailData."""
        with pytest.raises(ValidationError):
            EmailData(email="<EMAIL>", confidence=1.5)
        
        with pytest.raises(ValidationError):
            EmailData(email="<EMAIL>", confidence=-0.1)
    
    def test_phone_data_valid(self):
        """Test valid PhoneData."""
        phone = PhoneData(phone="******-123-4567", confidence=0.90)
        
        assert phone.phone == "******-123-4567"
        assert phone.confidence == 0.90
    
    def test_social_media_data_valid(self):
        """Test valid SocialMediaData."""
        social = SocialMediaData(
            platform="instagram",
            url="https://instagram.com/user",
            handle="@user",
            confidence=0.85
        )
        
        assert social.platform == "instagram"
        assert social.url == "https://instagram.com/user"
        assert social.handle == "@user"
        assert social.confidence == 0.85
    
    def test_social_media_data_minimal(self):
        """Test minimal SocialMediaData."""
        social = SocialMediaData(
            platform="facebook",
            url="https://facebook.com/page",
            confidence=0.80
        )
        
        assert social.platform == "facebook"
        assert social.url == "https://facebook.com/page"
        assert social.handle is None
        assert social.confidence == 0.80
    
    def test_contact_data_full(self):
        """Test ContactData with all fields."""
        email = EmailData(email="<EMAIL>", confidence=0.95)
        phone = PhoneData(phone="******-123-4567", confidence=0.90)
        social = SocialMediaData(
            platform="instagram",
            url="https://instagram.com/user",
            confidence=0.85
        )
        
        contact = ContactData(
            email=email,
            phone=phone,
            social_media=social
        )
        
        assert contact.email == email
        assert contact.phone == phone
        assert contact.social_media == social
    
    def test_contact_data_empty(self):
        """Test empty ContactData."""
        contact = ContactData()
        
        assert contact.email is None
        assert contact.phone is None
        assert contact.social_media is None
    
    def test_single_url_response_success(self, sample_contact_data, sample_metadata):
        """Test successful SingleUrlResponse."""
        response = SingleUrlResponse(
            success=True,
            url="https://example.com",
            timestamp=datetime.now(),
            processing_time=12.5,
            data=ContactData(**sample_contact_data),
            metadata=sample_metadata
        )
        
        assert response.success is True
        assert response.url == "https://example.com"
        assert response.processing_time == 12.5
        assert response.data is not None
        assert response.metadata is not None
        assert response.error is None
    
    def test_single_url_response_failure(self):
        """Test failed SingleUrlResponse."""
        response = SingleUrlResponse(
            success=False,
            url="https://example.com",
            timestamp=datetime.now(),
            processing_time=5.0,
            error={
                "code": "TIMEOUT_ERROR",
                "message": "Request timed out"
            }
        )
        
        assert response.success is False
        assert response.url == "https://example.com"
        assert response.processing_time == 5.0
        assert response.data is None
        assert response.metadata is None
        assert response.error is not None
    
    def test_error_response(self):
        """Test ErrorResponse."""
        error = ErrorResponse(
            error={
                "code": "INVALID_URL",
                "message": "The provided URL is not valid",
                "details": "URL must start with http:// or https://"
            },
            timestamp=datetime.now()
        )
        
        assert error.success is False
        assert error.error.code == "INVALID_URL"
        assert error.error.message == "The provided URL is not valid"
        assert error.error.details == "URL must start with http:// or https://"
    
    def test_health_response(self):
        """Test HealthResponse."""
        health = HealthResponse(
            status="healthy",
            timestamp=datetime.now(),
            version="1.0.0",
            uptime=3600,
            active_jobs=2
        )
        
        assert health.status == "healthy"
        assert health.version == "1.0.0"
        assert health.uptime == 3600
        assert health.active_jobs == 2
        assert health.system is None
    
    def test_health_response_with_system_info(self):
        """Test HealthResponse with system info."""
        health = HealthResponse(
            status="healthy",
            timestamp=datetime.now(),
            version="1.0.0",
            uptime=3600,
            active_jobs=2,
            system={
                "cpu_usage": 45.2,
                "memory_usage": 67.8,
                "disk_usage": 23.1
            }
        )
        
        assert health.system is not None
        assert health.system.cpu_usage == 45.2
        assert health.system.memory_usage == 67.8
        assert health.system.disk_usage == 23.1


class TestModelSerialization:
    """Test model serialization and deserialization."""
    
    def test_single_url_request_json(self):
        """Test SingleUrlRequest JSON serialization."""
        request = SingleUrlRequest(
            url="https://example.com",
            options=ScrapingOptions(max_pages=3, timeout=20)
        )
        
        json_data = request.model_dump()
        assert str(json_data["url"]) == "https://example.com/"
        assert json_data["options"]["max_pages"] == 3
        assert json_data["options"]["timeout"] == 20
    
    def test_single_url_response_json(self, sample_contact_data, sample_metadata):
        """Test SingleUrlResponse JSON serialization."""
        response = SingleUrlResponse(
            success=True,
            url="https://example.com",
            timestamp=datetime.now(),
            processing_time=12.5,
            data=ContactData(**sample_contact_data),
            metadata=sample_metadata
        )
        
        json_data = response.model_dump()
        assert json_data["success"] is True
        assert json_data["url"] == "https://example.com"
        assert json_data["processing_time"] == 12.5
        assert "data" in json_data
        assert "metadata" in json_data
    
    def test_batch_url_request_json(self):
        """Test BatchUrlRequest JSON serialization."""
        request = BatchUrlRequest(
            urls=["https://example1.com", "https://example2.com"],
            options=ScrapingOptions(max_pages=2)
        )
        
        json_data = request.model_dump()
        assert len(json_data["urls"]) == 2
        assert json_data["options"]["max_pages"] == 2
    
    def test_error_response_json(self):
        """Test ErrorResponse JSON serialization."""
        error = ErrorResponse(
            error={
                "code": "INVALID_URL",
                "message": "The provided URL is not valid"
            },
            timestamp=datetime.now()
        )
        
        json_data = error.model_dump()
        assert json_data["success"] is False
        assert json_data["error"]["code"] == "INVALID_URL"
        assert json_data["error"]["message"] == "The provided URL is not valid"
