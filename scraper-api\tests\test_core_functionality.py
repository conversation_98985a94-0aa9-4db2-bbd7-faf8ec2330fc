"""
Test core scraping functionality.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock

from app.core.scraper import WebScraperEngine
from app.core.extractor import ContactExtractor
from app.utils.validation import validate_url, validate_email, validate_phone


class TestContactExtractor:
    """Test ContactExtractor functionality."""
    
    @pytest.mark.asyncio
    async def test_extractor_initialization(self, contact_extractor):
        """Test ContactExtractor initialization."""
        assert contact_extractor is not None
        assert hasattr(contact_extractor, 'extract_contacts')
    
    def test_email_validation(self, contact_extractor):
        """Test email validation."""
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        invalid_emails = [
            "not-an-email",
            "@domain.com",
            "user@",
            "user@domain",
            ""
        ]
        
        for email in valid_emails:
            assert contact_extractor._is_valid_email(email), f"Should be valid: {email}"
        
        for email in invalid_emails:
            assert not contact_extractor._is_valid_email(email), f"Should be invalid: {email}"
    
    def test_phone_validation(self, contact_extractor):
        """Test phone validation."""
        valid_phones = [
            "******-123-4567",
            "(*************",
            "************",
            "+44 20 7946 0958"
        ]
        
        invalid_phones = [
            "123",
            "abc-def-ghij",
            "555-123",
            ""
        ]
        
        for phone in valid_phones:
            assert contact_extractor._is_valid_phone(phone), f"Should be valid: {phone}"
        
        for phone in invalid_phones:
            assert not contact_extractor._is_valid_phone(phone), f"Should be invalid: {phone}"
    
    def test_social_url_validation(self, contact_extractor):
        """Test social media URL validation."""
        valid_socials = [
            "https://instagram.com/user",
            "https://facebook.com/page",
            "https://twitter.com/handle",
            "https://linkedin.com/in/profile"
        ]
        
        invalid_socials = [
            "https://example.com",
            "https://google.com",
            "not-a-url",
            ""
        ]
        
        for social in valid_socials:
            assert contact_extractor._is_valid_social_url(social), f"Should be valid: {social}"
        
        for social in invalid_socials:
            assert not contact_extractor._is_valid_social_url(social), f"Should be invalid: {social}"
    
    def test_extract_from_html(self, contact_extractor, sample_html):
        """Test extraction from HTML content."""
        # Test the HTML parsing methods directly
        emails = contact_extractor._extract_emails_from_html(sample_html)
        phones = contact_extractor._extract_phones_from_html(sample_html)
        socials = contact_extractor._extract_social_media_from_html(sample_html, "https://example.com")

        assert isinstance(emails, list)
        assert isinstance(phones, list)
        assert isinstance(socials, list)
        
        # Should find at least one contact method
        has_contact = (
            result.get('email') is not None or
            result.get('phone') is not None or
            result.get('social_media') is not None
        )
        assert has_contact, "Should extract at least one contact method"
    
    def test_website_type_detection(self, contact_extractor):
        """Test website type detection."""
        test_cases = [
            ("https://company.com", "business"),
            ("https://hospital.org", "medical"),
            ("https://university.edu", "education"),
            ("https://city.gov", "government"),
            ("https://example.com", "business")  # default
        ]
        
        for url, expected_type in test_cases:
            detected_type = contact_extractor._detect_website_type(url)
            assert detected_type == expected_type, f"URL {url} should be {expected_type}, got {detected_type}"


class TestWebScraperEngine:
    """Test WebScraperEngine functionality."""
    
    @pytest.mark.asyncio
    async def test_engine_initialization(self, scraper_engine):
        """Test WebScraperEngine initialization."""
        assert scraper_engine is not None
        assert scraper_engine.batch_size == 2
        assert scraper_engine.max_concurrent == 1
        assert scraper_engine.default_timeout == 10
        assert scraper_engine.default_max_pages == 2
    
    def test_engine_stats(self, scraper_engine):
        """Test engine statistics."""
        stats = scraper_engine.get_stats()
        
        assert isinstance(stats, dict)
        assert "batch_size" in stats
        assert "max_concurrent" in stats
        assert "default_timeout" in stats
        assert "default_max_pages" in stats
        assert stats["batch_size"] == 2
        assert stats["max_concurrent"] == 1
    
    @pytest.mark.asyncio
    async def test_single_url_scraping_success(self, scraper_engine):
        """Test successful single URL scraping."""
        # Use a simple URL that should work
        url = "https://httpbin.org"
        
        result = await scraper_engine.scrape_single_url(
            url=url,
            max_pages=1,
            timeout=15,
            include_social=True,
            include_phone=True
        )
        
        assert isinstance(result, dict)
        assert "success" in result
        assert "url" in result
        assert "timestamp" in result
        assert "processing_time" in result
        assert result["url"] == url
        
        if result["success"]:
            assert "data" in result
            assert "metadata" in result
        else:
            assert "error" in result
    
    @pytest.mark.asyncio
    async def test_single_url_scraping_invalid_url(self, scraper_engine):
        """Test single URL scraping with invalid URL."""
        url = "not-a-valid-url"
        
        result = await scraper_engine.scrape_single_url(
            url=url,
            max_pages=1,
            timeout=10
        )
        
        assert isinstance(result, dict)
        assert "success" in result
        assert result["success"] is False
        assert "error" in result
    
    @pytest.mark.asyncio
    async def test_batch_url_scraping_success(self, scraper_engine):
        """Test successful batch URL scraping."""
        urls = [
            "https://httpbin.org",
            "https://example.com"
        ]
        
        result = await scraper_engine.scrape_batch_urls(
            urls=urls,
            max_pages=1,
            timeout=10,
            include_social=True,
            include_phone=True
        )
        
        assert isinstance(result, dict)
        assert "success" in result
        assert "total_processing_time" in result
        assert "timestamp" in result
        
        if result["success"]:
            assert "results" in result
            assert "summary" in result
            assert len(result["results"]) == len(urls)
            
            summary = result["summary"]
            assert "total_urls" in summary
            assert "successful" in summary
            assert "failed" in summary
            assert summary["total_urls"] == len(urls)
        else:
            assert "error" in result
    
    @pytest.mark.asyncio
    async def test_batch_url_scraping_empty_list(self, scraper_engine):
        """Test batch URL scraping with empty list."""
        urls = []
        
        result = await scraper_engine.scrape_batch_urls(
            urls=urls,
            max_pages=1,
            timeout=10
        )
        
        assert isinstance(result, dict)
        assert "success" in result
        assert result["success"] is False
        assert "error" in result
    
    @pytest.mark.asyncio
    async def test_batch_url_scraping_mixed_validity(self, scraper_engine):
        """Test batch URL scraping with mix of valid and invalid URLs."""
        urls = [
            "https://httpbin.org",  # Valid
            "not-a-valid-url",      # Invalid
            "https://example.com"   # Valid
        ]
        
        result = await scraper_engine.scrape_batch_urls(
            urls=urls,
            max_pages=1,
            timeout=10
        )
        
        assert isinstance(result, dict)
        assert "success" in result
        
        if result["success"]:
            assert "results" in result
            assert "summary" in result
            
            # Should have results for all URLs (some successful, some failed)
            assert len(result["results"]) <= len(urls)  # May filter out invalid URLs
            
            summary = result["summary"]
            assert summary["total_urls"] <= len(urls)
            assert summary["invalid_urls"] >= 1  # At least one invalid URL


class TestValidationUtils:
    """Test validation utilities."""
    
    def test_url_validation(self):
        """Test URL validation function."""
        valid_urls = [
            "https://example.com",
            "http://test.org",
            "https://subdomain.example.com/path?query=1"
        ]
        
        invalid_urls = [
            "not-a-url",
            "ftp://example.com",
            "http://",
            "",
            "javascript:alert('xss')"
        ]
        
        for url in valid_urls:
            assert validate_url(url), f"Should be valid: {url}"
        
        for url in invalid_urls:
            assert not validate_url(url), f"Should be invalid: {url}"
    
    def test_email_validation(self):
        """Test email validation function."""
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        invalid_emails = [
            "not-an-email",
            "@domain.com",
            "user@",
            ""
        ]
        
        for email in valid_emails:
            assert validate_email(email), f"Should be valid: {email}"
        
        for email in invalid_emails:
            assert not validate_email(email), f"Should be invalid: {email}"
    
    def test_phone_validation(self):
        """Test phone validation function."""
        valid_phones = [
            "******-123-4567",
            "(*************",
            "************"
        ]
        
        invalid_phones = [
            "123",
            "abc-def-ghij",
            ""
        ]
        
        for phone in valid_phones:
            assert validate_phone(phone), f"Should be valid: {phone}"
        
        for phone in invalid_phones:
            assert not validate_phone(phone), f"Should be invalid: {phone}"


class TestErrorHandling:
    """Test error handling in core functionality."""
    
    @pytest.mark.asyncio
    async def test_timeout_handling(self, scraper_engine):
        """Test timeout handling."""
        # Use a URL that will timeout
        url = "https://httpstat.us/200?sleep=30000"
        
        result = await scraper_engine.scrape_single_url(
            url=url,
            max_pages=1,
            timeout=1  # Very short timeout
        )
        
        assert isinstance(result, dict)
        assert "success" in result
        # Should either succeed quickly or fail with timeout
        if not result["success"]:
            assert "error" in result
    
    @pytest.mark.asyncio
    async def test_network_error_handling(self, scraper_engine):
        """Test network error handling."""
        # Use a URL that doesn't exist
        url = "https://this-domain-definitely-does-not-exist-12345.com"
        
        result = await scraper_engine.scrape_single_url(
            url=url,
            max_pages=1,
            timeout=10
        )
        
        assert isinstance(result, dict)
        assert "success" in result
        assert result["success"] is False
        assert "error" in result
    
    @pytest.mark.asyncio
    async def test_http_error_handling(self, scraper_engine):
        """Test HTTP error handling."""
        # Use a URL that returns 404
        url = "https://httpstat.us/404"
        
        result = await scraper_engine.scrape_single_url(
            url=url,
            max_pages=1,
            timeout=10
        )
        
        assert isinstance(result, dict)
        assert "success" in result
        # May succeed or fail depending on how we handle 404s
        if not result["success"]:
            assert "error" in result
