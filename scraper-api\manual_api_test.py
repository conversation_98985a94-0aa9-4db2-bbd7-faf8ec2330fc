#!/usr/bin/env python3
"""
Manual API test script to verify the Docker container is working properly.
"""

import requests
import json
import time
from datetime import datetime

API_BASE_URL = "http://localhost:8000"

def test_health_endpoint():
    """Test the health endpoint."""
    print("🏥 Testing Health Endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=10)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Status: {data.get('status')}")
            print(f"   Version: {data.get('version')}")
            print(f"   Uptime: {data.get('uptime')} seconds")
            print(f"   Active Jobs: {data.get('active_jobs')}")
            print("   ✅ Health endpoint working!")
            return True
        else:
            print(f"   ❌ Health endpoint failed: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ Health endpoint error: {e}")
        return False

def test_root_endpoint():
    """Test the root endpoint."""
    print("\n🏠 Testing Root Endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=10)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   API Name: {data.get('name')}")
            print(f"   Version: {data.get('version')}")
            print("   ✅ Root endpoint working!")
            return True
        else:
            print(f"   ❌ Root endpoint failed: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ Root endpoint error: {e}")
        return False

def test_single_url_scraping():
    """Test single URL scraping."""
    print("\n🎯 Testing Single URL Scraping...")
    try:
        payload = {
            "url": "https://httpbin.org",
            "options": {
                "max_pages": 1,
                "timeout": 15,
                "include_social": True,
                "include_phone": True
            }
        }
        
        print(f"   Scraping: {payload['url']}")
        start_time = time.time()
        
        response = requests.post(
            f"{API_BASE_URL}/scrape/single",
            json=payload,
            timeout=30
        )
        
        processing_time = time.time() - start_time
        print(f"   Status Code: {response.status_code}")
        print(f"   Processing Time: {processing_time:.2f}s")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Success: {data.get('success')}")
            print(f"   URL: {data.get('url')}")
            
            if data.get('success'):
                contact_data = data.get('data', {})
                metadata = data.get('metadata', {})
                
                print(f"   Email Found: {'Yes' if contact_data.get('email') else 'No'}")
                print(f"   Phone Found: {'Yes' if contact_data.get('phone') else 'No'}")
                print(f"   Social Found: {'Yes' if contact_data.get('social_media') else 'No'}")
                print(f"   Pages Checked: {metadata.get('pages_checked', 0)}")
                print(f"   Website Type: {metadata.get('website_type', 'unknown')}")
                print("   ✅ Single URL scraping working!")
                return True
            else:
                error = data.get('error', {})
                print(f"   ❌ Scraping failed: {error.get('message', 'Unknown error')}")
                return False
        else:
            print(f"   ❌ Single URL scraping failed: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ Single URL scraping error: {e}")
        return False

def test_batch_url_scraping():
    """Test batch URL scraping."""
    print("\n📦 Testing Batch URL Scraping...")
    try:
        payload = {
            "urls": [
                "https://httpbin.org",
                "https://example.com"
            ],
            "options": {
                "max_pages": 1,
                "timeout": 10,
                "include_social": True,
                "include_phone": True
            }
        }
        
        print(f"   Scraping {len(payload['urls'])} URLs")
        start_time = time.time()
        
        response = requests.post(
            f"{API_BASE_URL}/scrape/batch",
            json=payload,
            timeout=60
        )
        
        processing_time = time.time() - start_time
        print(f"   Status Code: {response.status_code}")
        print(f"   Processing Time: {processing_time:.2f}s")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Success: {data.get('success')}")
            
            if data.get('success'):
                summary = data.get('summary', {})
                print(f"   Total URLs: {summary.get('total_urls', 0)}")
                print(f"   Successful: {summary.get('successful', 0)}")
                print(f"   Failed: {summary.get('failed', 0)}")
                print(f"   Emails Found: {summary.get('emails_found', 0)}")
                print(f"   Phones Found: {summary.get('phones_found', 0)}")
                print(f"   Socials Found: {summary.get('socials_found', 0)}")
                print("   ✅ Batch URL scraping working!")
                return True
            else:
                error = data.get('error', {})
                print(f"   ❌ Batch scraping failed: {error.get('message', 'Unknown error')}")
                return False
        else:
            print(f"   ❌ Batch URL scraping failed: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ Batch URL scraping error: {e}")
        return False

def test_error_handling():
    """Test error handling with invalid requests."""
    print("\n⚠️  Testing Error Handling...")
    try:
        # Test invalid URL
        payload = {
            "url": "not-a-valid-url",
            "options": {
                "max_pages": 1,
                "timeout": 10
            }
        }
        
        response = requests.post(
            f"{API_BASE_URL}/scrape/single",
            json=payload,
            timeout=15
        )
        
        print(f"   Invalid URL Status Code: {response.status_code}")
        
        if response.status_code == 422:  # Validation error
            print("   ✅ Invalid URL properly rejected!")
            return True
        elif response.status_code == 200:
            data = response.json()
            if not data.get('success'):
                print("   ✅ Invalid URL handled gracefully!")
                return True
        
        print(f"   ❌ Error handling unexpected: {response.text}")
        return False
    except Exception as e:
        print(f"   ❌ Error handling test error: {e}")
        return False

def main():
    """Run all API tests."""
    print("🚀 MANUAL API TESTING")
    print("Testing the Docker Scraper API...")
    print("=" * 50)
    
    tests = [
        ("Health Endpoint", test_health_endpoint),
        ("Root Endpoint", test_root_endpoint),
        ("Single URL Scraping", test_single_url_scraping),
        ("Batch URL Scraping", test_batch_url_scraping),
        ("Error Handling", test_error_handling)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("The Docker Scraper API is working perfectly!")
    else:
        print("⚠️  SOME TESTS FAILED")
        print("Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
